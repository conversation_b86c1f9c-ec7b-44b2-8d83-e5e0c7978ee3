name: Dependency Submission

on:
  push:
    branches: [ 'develop' ]

permissions:
  contents: write

jobs:
  dependency-submission:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: corretto
          java-version: 17

      - name: Generate and submit dependency graph
        uses: gradle/actions/dependency-submission@v4
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
          GH_USERNAME: ${{ secrets.GH_USERNAME }}
        with:
          # Free Develocity Build Scan
          build-scan-publish: true
          build-scan-terms-of-use-url: "https://gradle.com/help/legal-terms-of-use"
          build-scan-terms-of-use-agree: "yes"
          # By default, failure to generate a dependency graph will cause the workflow to fail
          dependency-graph-continue-on-failure: true
          # Enable configuration-cache reuse for this build.
          cache-encryption-key: ${{ secrets.GRADLE_ENCRYPTION_KEY }}
          # cache-disabled: true
          # additional-arguments: --no-configuration-cache
          # Use a particular Gradle version instead of the configured wrapper.
          # gradle-version: 8.6