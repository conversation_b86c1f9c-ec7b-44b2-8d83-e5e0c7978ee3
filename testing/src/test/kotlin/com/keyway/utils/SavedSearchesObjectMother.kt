package com.keyway.utils

import com.keyway.adapters.dtos.me.BooleanValue
import com.keyway.adapters.dtos.me.BusinessTypeRequest
import com.keyway.adapters.dtos.me.BusinessTypeResponse
import com.keyway.adapters.dtos.me.CreateSavedSearchRequest
import com.keyway.adapters.dtos.me.IntValue
import com.keyway.adapters.dtos.me.SavedSearchFiltersRequest
import com.keyway.adapters.dtos.me.SavedSearchFiltersResponse
import com.keyway.adapters.dtos.me.SavedSearchResponse
import com.keyway.adapters.dtos.me.StringArrayValue
import com.keyway.adapters.dtos.me.StringValue
import com.keyway.core.dto.me.SavedSearchFiltersInput
import com.keyway.core.dto.me.UpdateSavedSearchInput
import com.keyway.core.entities.BusinessType
import com.keyway.core.entities.SavedSearch
import com.keyway.core.entities.SavedSearchFilters

object SavedSearchesObjectMother {
    fun oneCreateSavedSearchRequest(): CreateSavedSearchRequest {
        return CreateSavedSearchRequest(
            name = "Search 1",
            description = "This is the first search",
            businessType = BusinessTypeRequest.TRIPLE_NET,
            msaIds = listOf("msa123", "msa456"),
            zipCodeIds = listOf("zip123", "zip456"),
            totalProperties = 50,
            filters = SavedSearchFiltersRequest(
                marketFilters = mapOf(
                    "minPrice" to IntValue(100000),
                    "maxPrice" to IntValue(500000),
                    "propertyType" to StringValue("Office")
                ),
                propertyFilters = mapOf(
                    "bathrooms" to IntValue(1),
                    "bedrooms" to IntValue(2),
                    "location" to StringValue("Dallas")
                ),
                selectedBoundaries = listOf("boundary1", "boundary2")
            )
        )
    }

    fun oneSavedSearch(id: String = "123"): SavedSearch {
        return SavedSearch(
            id = id,
            name = "Search 1",
            description = "This is the first search",
            businessType = BusinessType.TRIPLE_NET,
            msaIds = listOf("msa123", "msa456"),
            zipCodeIds = listOf("zip123", "zip456"),
            totalProperties = 50,
            filters = SavedSearchFilters(
                marketFilters = mapOf(
                    "minPrice" to 100000,
                    "maxPrice" to 500000,
                    "propertyType" to "Office",
                    "class" to listOf("A", "C"),
                    "isActive" to true
                ),
                propertyFilters = mapOf(
                    "bathrooms" to 1,
                    "bedrooms" to 2,
                    "location" to "Dallas"
                ),
                selectedBoundaries = listOf("boundary1", "boundary2")
            )
        )
    }

    fun oneSavedSearchInput(id: String = "123") =
        UpdateSavedSearchInput(
            id = id,
            userId = "usr-1234",
            name = "name",
            description = "given description",
            businessType = BusinessType.TRIPLE_NET,
            msaIds = listOf("99100"),
            zipCodeIds = listOf("33100"),
            totalProperties = 5,
            filters = SavedSearchFiltersInput(
                marketFilters = mapOf(
                    "minPrice" to 100000,
                    "maxPrice" to 500000,
                    "propertyType" to "Office"
                ),
                propertyFilters = mapOf(
                    "bathrooms" to 1,
                    "bedrooms" to 2,
                    "location" to "Dallas"
                ),
                selectedBoundaries = listOf()
            )
        )

    fun oneSavedSearchResponse(id: String = "123"): SavedSearchResponse {
        return SavedSearchResponse(
            id = id,
            name = "Search 1",
            description = "This is the first search",
            businessType = BusinessTypeResponse.TRIPLE_NET,
            msaIds = listOf("msa123", "msa456"),
            zipCodeIds = listOf("zip123", "zip456"),
            totalProperties = 50,
            filters = SavedSearchFiltersResponse(
                marketFilters = mapOf(
                    "minPrice" to IntValue(100000),
                    "maxPrice" to IntValue(500000),
                    "propertyType" to StringValue("Office"),
                    "class" to StringArrayValue(listOf("A", "C")),
                    "isActive" to BooleanValue(true)
                ),
                propertyFilters = mapOf(
                    "bathrooms" to IntValue(1),
                    "bedrooms" to IntValue(2),
                    "location" to StringValue("Dallas")
                ),
                selectedBoundaries = listOf("boundary1", "boundary2")
            )
        )
    }
}
