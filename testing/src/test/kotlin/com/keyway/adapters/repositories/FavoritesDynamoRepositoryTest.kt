package com.keyway.adapters.repositories

import com.keyway.application.utils.base.BaseApplicationTest
import com.keyway.core.entities.Favorite
import com.keyway.core.repositories.FavoritesRepository
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInRelativeOrder
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class FavoritesDynamoRepositoryTest : BaseApplicationTest() {

    private val target: FavoritesRepository by inject()

    @Test
    fun `Given a user without saved favorites it should return an empty list`() {
        val givenUserId = "1234"

        val actual = target.getFavorites(givenUserId)

        assertThat(actual, equalTo(emptyList()))
    }

    @Test
    fun `Getting favorites for a given user should retrieve the stored data`() {
        val givenUserId = "123"
        val aPropertyId = "USTX-000001"
        val anotherPropertyId = "USTX-000002"
        val givenFolderName = "main"

        target.saveFavorite(givenUserId, Favorite(aPropertyId, givenFolderName))
        target.saveFavorite(givenUserId, Favorite(anotherPropertyId, givenFolderName))

        val actual = target.getFavorites(givenUserId)

        val expectedResponse = listOf(
            Favorite(anotherPropertyId, givenFolderName),
            Favorite(aPropertyId, givenFolderName)
        )

        assertThat(actual, containsInRelativeOrder(*expectedResponse.toTypedArray()))
    }

    @Test
    fun `Getting favorites for a given user should retrieve the stored data ordered by the most recent`() {
        val givenUserId = "123"
        val aPropertyId = "USTX-000001"
        val bPropertyId = "USTX-000002"
        val cPropertyId = "USTX-000003"
        val givenFolderName = "main"

        target.saveFavorite(givenUserId, Favorite(aPropertyId, givenFolderName))
        target.saveFavorite(givenUserId, Favorite(bPropertyId, givenFolderName))
        target.saveFavorite(givenUserId, Favorite(cPropertyId, givenFolderName))
        target.saveFavorite(givenUserId, Favorite(bPropertyId, givenFolderName))

        val actual = target.getFavorites(givenUserId)

        val expectedResponse = listOf(
            Favorite(bPropertyId, givenFolderName),
            Favorite(cPropertyId, givenFolderName),
            Favorite(aPropertyId, givenFolderName)
        )

        assertThat(actual, containsInRelativeOrder(*expectedResponse.toTypedArray()))
    }

    @Test
    fun `Deleting all favorites for a given user should result in an empty list`() {
        val givenUserId = "123"
        val aPropertyId = "USTX-000001"
        val bPropertyId = "USTX-000002"
        val givenFolderName = "main"

        target.saveFavorite(givenUserId, Favorite(aPropertyId, givenFolderName))
        target.saveFavorite(givenUserId, Favorite(bPropertyId, givenFolderName))
        target.deleteFavorite(givenUserId, bPropertyId)
        target.deleteFavorite(givenUserId, aPropertyId)

        val actual = target.getFavorites(givenUserId)

        assertThat(actual, equalTo(emptyList()))
    }
}
