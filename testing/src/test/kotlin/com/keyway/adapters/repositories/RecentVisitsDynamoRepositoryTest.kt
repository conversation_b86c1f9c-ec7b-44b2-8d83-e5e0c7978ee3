package com.keyway.adapters.repositories

import com.keyway.application.utils.base.BaseApplicationTest
import com.keyway.core.repositories.RecentVisitsRepository
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.containsInRelativeOrder
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class RecentVisitsDynamoRepositoryTest : BaseApplicationTest() {

    private val target: RecentVisitsRepository by inject()

    @Test
    fun `Given a user without saved recently visits it should return an empty list`() {
        val givenUserId = "1234"

        val actual = target.getRecentVisits(givenUserId, null)

        assertThat(actual, Matchers.equalTo(emptyList()))
    }

    @Test
    fun `Getting recently visited for a given user should retrieve the stored data`() {
        val givenUserId = "123"
        val aPropertyId = "USTX-000001"
        val anotherPropertyId = "USTX-000002"

        target.saveRecentVisits(givenUserId, aPropertyId)
        target.saveRecentVisits(givenUserId, anotherPropertyId)

        val actual = target.getRecentVisits(givenUserId, 5)

        assertThat(actual, containsInRelativeOrder(anotherPropertyId, aPropertyId))
    }

    @Test
    fun `Adding recently visited for an already visited property, should be stored in the first position`() {
        val givenUserId = "123"
        val aPropertyId = "USTX-000001"
        val anotherPropertyId = "USTX-000002"

        target.saveRecentVisits(givenUserId, aPropertyId)
        target.saveRecentVisits(givenUserId, anotherPropertyId)
        target.saveRecentVisits(givenUserId, aPropertyId)

        val actual = target.getRecentVisits(givenUserId, 5)

        assertThat(actual, containsInRelativeOrder(aPropertyId, anotherPropertyId))
    }

    @Test
    fun `Given a user with recent visits, it should return the limit value`() {
        val givenUserId = "123"
        val limit = 7
        val givenProperties = buildList {
            for (x in 1..9) {
                add("USTX-00000$x")
            }
        }

        givenProperties.forEach {
            target.saveRecentVisits(givenUserId, it)
        }

        val actual = target.getRecentVisits(givenUserId, limit)

        assertThat(actual, containsInRelativeOrder(*givenProperties.takeLast(limit).reversed().toTypedArray()))
    }
}
