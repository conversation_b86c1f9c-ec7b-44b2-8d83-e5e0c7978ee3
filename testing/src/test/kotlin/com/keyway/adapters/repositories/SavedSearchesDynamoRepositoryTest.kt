package com.keyway.adapters.repositories

import com.keyway.application.utils.base.BaseApplicationTest
import com.keyway.core.entities.BusinessType
import com.keyway.core.entities.SavedSearch
import com.keyway.core.entities.SavedSearchFilters
import com.keyway.core.repositories.SavedSearchesRepository
import kotlin.test.assertTrue
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInRelativeOrder
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class SavedSearchesDynamoRepositoryTest : BaseApplicationTest() {

    private val target: SavedSearchesRepository by inject()

    @Test
    fun `Given a user without saved searches it should return an empty list`() {
        val givenUserId = "1234"

        val actual = target.getSavedSearches(givenUserId)

        assertThat(actual, equalTo(emptyList()))
    }

    @Test
    fun `Getting saved searches for a given user should retrieve the stored data`() {
        val givenUserId = "123"
        val givenAnotherUserId = "another"
        val givenSavedSearch1 = SavedSearch(
            id = "1",
            name = "Search 1",
            description = "Description for Search 1",
            businessType = BusinessType.MULTIFAMILY,
            msaIds = listOf("MSA1", "MSA2"),
            zipCodeIds = listOf("ZIP1", "ZIP2"),
            totalProperties = 100,
            filters = SavedSearchFilters(
                marketFilters = mapOf("city" to "New York", "state" to "NY"),
                propertyFilters = mapOf("minPrice" to 100000, "maxPrice" to 500000),
                selectedBoundaries = listOf("Boundary1", "Boundary2")
            )
        )
        val givenSavedSearch2 = SavedSearch(
            id = "2",
            name = "Search 2",
            description = null,
            businessType = BusinessType.TRIPLE_NET,
            msaIds = listOf("MSA3"),
            zipCodeIds = emptyList(),
            totalProperties = 50,
            filters = SavedSearchFilters(
                marketFilters = mapOf("city" to "Los Angeles", "state" to "CA"),
                propertyFilters = emptyMap(),
                selectedBoundaries = emptyList()
            )
        )
        val givenSavedSearchForAnotherUser = SavedSearch(
            id = "129",
            name = "Search 1",
            description = "Description for Search 1",
            businessType = BusinessType.MULTIFAMILY,
            msaIds = listOf("MSA1", "MSA2"),
            zipCodeIds = listOf("ZIP1", "ZIP2"),
            totalProperties = 100,
            filters = SavedSearchFilters(
                marketFilters = mapOf("city" to "New York", "state" to "NY"),
                propertyFilters = mapOf("minPrice" to 100000, "maxPrice" to 500000),
                selectedBoundaries = listOf("Boundary1", "Boundary2")
            )
        )

        target.upsertSaveSearch(givenUserId, givenSavedSearch1)
        target.upsertSaveSearch(givenUserId, givenSavedSearch2)
        target.upsertSaveSearch(givenAnotherUserId, givenSavedSearchForAnotherUser)

        val actual = target.getSavedSearches(givenUserId)

        assertThat(actual, containsInRelativeOrder(givenSavedSearch2, givenSavedSearch1))
    }

    @Test
    fun `Updating an existing saved search for a given user, then retrieve the stored data`() {
        val givenUserId = "123"
        val givenSavedSearch = SavedSearch(
            id = "1",
            name = "Search 1",
            description = "Description for Search 1",
            businessType = BusinessType.MULTIFAMILY,
            msaIds = listOf("MSA1", "MSA2"),
            zipCodeIds = listOf("ZIP1", "ZIP2"),
            totalProperties = 100,
            filters = SavedSearchFilters(
                marketFilters = mapOf("city" to "New York", "state" to "NY"),
                propertyFilters = mapOf("minPrice" to 100000, "maxPrice" to 500000),
                selectedBoundaries = listOf("Boundary1", "Boundary2")
            )
        )
        val updatedSavedSearch = givenSavedSearch.copy(description = "Updated description")

        target.upsertSaveSearch(givenUserId, givenSavedSearch)
        target.upsertSaveSearch(givenUserId, updatedSavedSearch)

        val actual = target.getSavedSearches(givenUserId)

        assertThat(actual.first(), equalTo(updatedSavedSearch))
    }

    @Test
    fun `Creating a saved search for a given user, then delete it`() {
        val givenUserId = "123"
        val givenSavedSearch = SavedSearch(
            id = "1",
            name = "Search 1",
            description = "Description for Search 1",
            businessType = BusinessType.MULTIFAMILY,
            msaIds = listOf("MSA1", "MSA2"),
            zipCodeIds = listOf("ZIP1", "ZIP2"),
            totalProperties = 100,
            filters = SavedSearchFilters(
                marketFilters = mapOf("city" to "New York", "state" to "NY"),
                propertyFilters = mapOf("minPrice" to 100000, "maxPrice" to 500000),
                selectedBoundaries = listOf("Boundary1", "Boundary2")
            )
        )

        target.upsertSaveSearch(givenUserId, givenSavedSearch)
        target.deleteSavedSearch(givenUserId, givenSavedSearch.id)

        val actual = target.getSavedSearches(givenUserId)

        assertTrue(actual.isEmpty())
    }

    @Test
    fun `Delete a saved search that does not exists should return no errors`() {
        val givenUserId = "123"

        target.deleteSavedSearch(givenUserId, "invalid-id")

        val actual = target.getSavedSearches(givenUserId)

        assertTrue(actual.isEmpty())
    }
}
