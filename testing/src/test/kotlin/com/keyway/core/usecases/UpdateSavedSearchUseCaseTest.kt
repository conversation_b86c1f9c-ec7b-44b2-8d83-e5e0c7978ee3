package com.keyway.core.usecases

import com.keyway.core.dto.me.SavedSearchIdOutput
import com.keyway.core.entities.SavedSearch
import com.keyway.core.entities.SavedSearchFilters
import com.keyway.core.exceptions.SavedSearchNotFoundException
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.core.usecases.me.UpdateSavedSearchUseCase
import com.keyway.utils.SavedSearchesObjectMother.oneSavedSearch
import com.keyway.utils.SavedSearchesObjectMother.oneSavedSearchInput
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import java.util.*
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class UpdateSavedSearchUseCaseTest {

    private val savedSearchesRepositoryMock = mockk<SavedSearchesRepository>()
    private val target = UpdateSavedSearchUseCase(savedSearchesRepositoryMock)

    @Test
    fun `Given a existing saved search it should call the repository with the correct entity to save`() {
        val givenId = UUID.randomUUID().toString()
        val givenInput = oneSavedSearchInput(id = givenId)
        val givenSavedSearch = oneSavedSearch(id = givenId)

        every {
            savedSearchesRepositoryMock.getSavedSearch(givenInput.userId, givenInput.id)
        } returns givenSavedSearch

        every {
            savedSearchesRepositoryMock.upsertSaveSearch(
                givenInput.userId,
                SavedSearch(
                    id = givenInput.id,
                    name = givenInput.name,
                    description = givenInput.description,
                    businessType = givenInput.businessType,
                    msaIds = givenInput.msaIds,
                    zipCodeIds = givenInput.zipCodeIds,
                    totalProperties = givenInput.totalProperties,
                    filters = SavedSearchFilters(
                        marketFilters = givenInput.filters.marketFilters,
                        propertyFilters = givenInput.filters.propertyFilters,
                        selectedBoundaries = givenInput.filters.selectedBoundaries
                    )
                )
            )
        } just runs

        val actual = target.execute(givenInput)
        assertThat(actual, equalTo(SavedSearchIdOutput(givenInput.id)))
    }

    @Test
    fun `Given a non existing saved search it should throw an exception`() {
        val givenInput = oneSavedSearchInput(UUID.randomUUID().toString())

        every {
            savedSearchesRepositoryMock.getSavedSearch(givenInput.userId, givenInput.id)
        } returns null

        assertThrows<SavedSearchNotFoundException> { target.execute(givenInput) }
    }
}
