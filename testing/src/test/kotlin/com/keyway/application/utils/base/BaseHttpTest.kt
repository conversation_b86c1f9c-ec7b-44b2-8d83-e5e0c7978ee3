package com.keyway.application.utils.base

import com.keyway.adapters.dtos.error.response.RestExceptionResponse
import com.keyway.application.utils.AuthMock
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.Test

abstract class BaseHttpTest(
    private val method: HttpMethod,
    private val baseSecuredRoute: String
) : BaseApplicationTest() {

    @Test
    protected fun `Given a user without token it should get a 401 error`() {
        // Given
        val givenUrl = "${localUrl()}$baseSecuredRoute"

        // When
        val result = Unirest
            .request(method.name(), givenUrl)
            .asString()

        // Then
        JsonMapper.decode(result.body, RestExceptionResponse::class.java).let { response ->
            MatcherAssert.assertThat(response.statusCode, Matchers.equalTo(HttpStatus.UNAUTHORIZED_401))
            MatcherAssert.assertThat(response.message, Matchers.containsString("401 | MISSED_AUTHORIZATION - Authorization header is mandatory"))
        }
    }

    @Test
    protected fun `Given a user with an invalid token type it should get a 401 error`() {
        // Given
        val givenUrl = "${localUrl()}$baseSecuredRoute"

        // When
        val result = Unirest
            .request(method.name(), givenUrl)
            .headers(AuthMock.getInvalidAuthHeader())
            .asString()

        // Then
        JsonMapper.decode(result.body, RestExceptionResponse::class.java).let { response ->
            MatcherAssert.assertThat(response.statusCode, Matchers.equalTo(HttpStatus.UNAUTHORIZED_401))
            MatcherAssert.assertThat(response.message, Matchers.containsString("401 | INVALID_TOKEN_TYPE - Invalid token type"))
        }
    }
}
