package com.keyway.application.utils.base

import com.keyway.application.Application
import com.keyway.application.configuration.model.SystemConfig
import com.keyway.application.configuration.parser.ConfigParser
import com.keyway.application.koin.modules.loader.ModuleLoader
import com.keyway.application.utils.module.TestModule
import com.keyway.kommons.mapper.jackson.useDefaultJsonConfig
import kong.unirest.Unirest
import kong.unirest.jackson.JacksonObjectMapper
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.test.KoinTest
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement
import software.amazon.awssdk.services.dynamodb.model.KeyType
import software.amazon.awssdk.services.dynamodb.model.LocalSecondaryIndex
import software.amazon.awssdk.services.dynamodb.model.Projection
import software.amazon.awssdk.services.dynamodb.model.ProjectionType
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType

abstract class BaseApplicationTest : KoinTest {

    private val systemConfig: SystemConfig by inject()

    protected fun localUrl() = "http://localhost:${systemConfig.httpPort}"

    companion object : KoinComponent {

        private val dynamoDbClient: DynamoDbAsyncClient by inject()
        private var isInitialized = false

        @JvmStatic
        @BeforeAll
        fun init() {
            if (isInitialized.not()) {
                Unirest.config().objectMapper = JacksonObjectMapper(useDefaultJsonConfig())
                ConfigParser.read(isTest = true)
                ModuleLoader.modules.add(TestModule.modules)
                Application.main(arrayOf())
                isInitialized = true
            }
        }
    }

    @BeforeEach
    fun cleanApplication() {
        recreateDatabase()
    }

    private fun recreateDatabase() {
        val tableName = "user-activity_local"
        runCatching {
            dynamoDbClient.deleteTable(
                DeleteTableRequest.builder()
                    .tableName(tableName)
                    .build()
            ).get()
        }.also {
            val lsi = LocalSecondaryIndex.builder()
                .indexName("LSI1") // Index name for the LSI
                .keySchema(
                    KeySchemaElement.builder().attributeName("PK").keyType(KeyType.HASH).build(),
                    KeySchemaElement.builder().attributeName("LSI1-SK").keyType(KeyType.RANGE).build()
                )
                .projection(Projection.builder().projectionType(ProjectionType.ALL).build())
                .build()

            dynamoDbClient.createTable(
                CreateTableRequest.builder()
                    .tableName(tableName)
                    .attributeDefinitions(
                        AttributeDefinition.builder()
                            .attributeName("PK")
                            .attributeType(ScalarAttributeType.S)
                            .build(),
                        AttributeDefinition.builder()
                            .attributeName("SK")
                            .attributeType(ScalarAttributeType.S)
                            .build(),
                        AttributeDefinition.builder()
                            .attributeName("LSI1-SK")
                            .attributeType(ScalarAttributeType.S)
                            .build()
                    )
                    .keySchema(
                        KeySchemaElement.builder()
                            .attributeName("PK")
                            .keyType(KeyType.HASH)
                            .build(),
                        KeySchemaElement.builder()
                            .attributeName("SK")
                            .keyType(KeyType.RANGE)
                            .build()
                    )
                    .localSecondaryIndexes(lsi)
                    .provisionedThroughput(
                        ProvisionedThroughput.builder()
                            .readCapacityUnits(1L)
                            .writeCapacityUnits(1L)
                            .build()
                    )
                    .build()
            ).get()
        }
    }
}
