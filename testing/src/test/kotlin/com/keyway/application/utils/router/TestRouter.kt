package com.keyway.application.utils.router

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.application.javalin.Router
import io.javalin.Javalin

class TestRouter constructor(
    private val app: Javalin
) : Router {

    override fun setUpRoutes() {
        app.get("/test/exception/bad-request") {
            throw BadRequestException()
        }

        app.get("/test/exception/runtime") {
            throw RuntimeException()
        }
    }
}
