package com.keyway.application.utils.module

import com.keyway.application.router.health.HealthCheckRouter
import com.keyway.application.router.me.FavoritesRouter
import com.keyway.application.router.me.RecentVisitsRouter
import com.keyway.application.router.me.SavedSearchesRouter
import com.keyway.application.utils.router.TestRouter
import com.keyway.core.repositories.KeyGenerator
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.domain.token.UserToken
import io.mockk.every
import io.mockk.mockk
import org.koin.core.qualifier.named
import org.koin.dsl.module

object TestModule {

    val modules = module(createdAtStart = true) {

        single<KeyGenerator> {
            mockk {
                every { generateKey() } returns "d1b82fc0-7f69-4d84-9533-7429f744d578"
            }
        }

        single<TokenSdk> {
            mockk {
                every { validate(any()) } returns UserToken("123", mockk(relaxed = true))
            }
        }

        // Routers
        single(named("routes")) {
            setOf(
                TestRouter(get()),
                HealthCheckRouter(get(), get()),
                RecentVisitsRouter(get(), get(), get()),
                FavoritesRouter(get(), get(), get(), get()),
                SavedSearchesRouter(get(), get(), get(), get(), get())
            )
        }
    }
}
