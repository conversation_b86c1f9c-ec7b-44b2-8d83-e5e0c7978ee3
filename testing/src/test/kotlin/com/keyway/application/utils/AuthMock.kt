package com.keyway.application.utils

import org.apache.http.HttpHeaders

object AuthMock {

    const val authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.dyt0CoTl4WoVjAHI9Q_CwSKhl6d_9rhM3NrXuJttkao"

    fun getAuthHeader(token: String = authToken) = mapOf(HttpHeaders.AUTHORIZATION to getAuthBearerToken(token))

    fun getInvalidAuthHeader(token: String = authToken) = mapOf(HttpHeaders.AUTHORIZATION to "invalid header")

    fun getAuthBearerToken(token: String = authToken) = "Bearer $token"
}
