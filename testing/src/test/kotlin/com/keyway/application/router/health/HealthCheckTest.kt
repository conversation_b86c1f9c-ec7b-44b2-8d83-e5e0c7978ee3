package com.keyway.application.router.health

import com.keyway.application.utils.base.BaseApplicationTest
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test

class HealthCheckTest : BaseApplicationTest() {

    @Test
    fun `Health check test`() {
        // Given
        val givenUrl = "${localUrl()}/health"

        // When
        val result = Unirest.get(givenUrl).asString()

        // Then
        assertThat(JsonMapper.decode(result.body, Map::class.java)["status"], equalTo("ok"))
    }
}
