package com.keyway.application.router.me

import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.entities.Favorite
import com.keyway.core.repositories.FavoritesRepository
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class DeleteFavoriteTest : BaseHttpTest(HttpMethod.DELETE, "/me/favorites") {

    private val favoritesRepository: FavoritesRepository by inject()

    @BeforeEach
    fun setup() {
        favoritesRepository.saveFavorite("123", Favorite("USTX-000091", "main"))
        favoritesRepository.saveFavorite("123", Favorite("USTX-000092", "main"))
    }

    @Test
    fun `Given a user with saved favorites it should be able to delete a favorite`() {
        // Given
        val givenPropertyId = "USTX-000091"
        val givenUrl = "${localUrl()}/me/favorites/$givenPropertyId"

        // When
        val result = Unirest
            .delete(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        assertThat(result.status, equalTo(HttpStatus.ACCEPTED_202))
    }
}
