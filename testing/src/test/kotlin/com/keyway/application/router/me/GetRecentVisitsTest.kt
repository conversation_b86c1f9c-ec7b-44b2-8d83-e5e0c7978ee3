package com.keyway.application.router.me

import com.keyway.adapters.dtos.me.GetRecentVisitsResponse
import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.repositories.RecentVisitsRepository
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class GetRecentVisitsTest : BaseHttpTest(HttpMethod.GET, "/me/recent-visits") {

    private val recentVisitsRepository: RecentVisitsRepository by inject()

    @BeforeEach
    fun setup() {
        recentVisitsRepository.saveRecentVisits("123", "USTX-000091")
        recentVisitsRepository.saveRecentVisits("123", "USTX-000092")
    }

    @Test
    fun `Given a user with saved favorites it should be able to get the list of them`() {
        // Given
        val givenUrl = "${localUrl()}/me/recent-visits"

        // When
        val result = Unirest
            .get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        val expectedResponse = GetRecentVisitsResponse(
            propertyIds = listOf("USTX-000092", "USTX-000091")
        )

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
        JsonMapper.decode(result.body, GetRecentVisitsResponse::class.java)
            .let { response ->
                assertThat(response, equalTo(expectedResponse))
            }
    }
}
