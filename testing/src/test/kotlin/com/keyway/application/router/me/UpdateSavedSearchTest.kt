package com.keyway.application.router.me

import com.keyway.adapters.dtos.error.response.RestExceptionResponse
import com.keyway.adapters.dtos.me.SavedSearchIdResponse
import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.utils.SavedSearchesObjectMother.oneSavedSearch
import io.mockk.spyk
import io.mockk.verify
import kong.unirest.HttpMethod
import kong.unirest.HttpStatus.NOT_FOUND
import kong.unirest.HttpStatus.OK
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class UpdateSavedSearchTest : BaseHttpTest(HttpMethod.PUT, "/me/saved-searches/123") {

    private val savedSearchesRepository: SavedSearchesRepository by inject()
    private val savedSearchesRepositorySpy = spyk(savedSearchesRepository)

    @BeforeEach
    fun setUp() {
        savedSearchesRepository.upsertSaveSearch(
            "123",
            oneSavedSearch(id = "b03324a1-f0ab-494c-9e3e-016991523c31")
        )
    }

    @Test
    fun `Given a user updating an existing saved search it should be able to add a new one`() {
        // Given
        val givenUrl = "${localUrl()}/me/saved-searches/b03324a1-f0ab-494c-9e3e-016991523c31"
        val givenBody = """
            {
              "name": "Example Saved Search",
              "description": "This is an example saved search",
              "businessType": "TRIPLE_NET",
              "msaIds": ["msa123", "msa456"],
              "zipCodeIds": ["zip123", "zip456"],
              "totalProperties": 100,
              "filters": {
                "marketFilters": { 
                    "minPrice": { "value": 100000 },
                    "maxPrice": { "value": 500000 },
                    "propertyType": { "value": "Office" }
                },
                "propertyFilters": { 
                  "minSize": { "value": 2000 },
                  "maxSize": { "value": 5000 },
                  "bedrooms": { "value": 2 },
                  "bathrooms": { "value": 2 }
                },
                "selectedBoundaries": ["boundary1", "boundary2"]
              }
            }
        """.trimIndent()

        // When
        val result = Unirest
            .put(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(givenBody)
            .asString()

        // Then
        assertThat(result.status, equalTo(OK))
        JsonMapper.decode(result.body, SavedSearchIdResponse::class.java)
            .also { response ->
                assertThat(response.id, equalTo("b03324a1-f0ab-494c-9e3e-016991523c31"))
            }
    }

    @Test
    fun `Given a user updating a non existing saved search it should return a not found error`() {
        // Given
        val givenUrl = "${localUrl()}/me/saved-searches/b03324a1"
        val givenBody = """
            {
              "name": "Example Saved Search",
              "description": "This is an example saved search",
              "businessType": "TRIPLE_NET",
              "msaIds": ["msa123", "msa456"],
              "zipCodeIds": ["zip123", "zip456"],
              "totalProperties": 100,
              "filters": {
                "marketFilters": { 
                    "minPrice": { "value": 100000 },
                    "maxPrice": { "value": 500000 },
                    "propertyType": { "value": "Office" },
                    "isActive": { "value": true },
                    "class": { "value": ["A", "C"] }
                },
                "propertyFilters": { 
                  "minSize": { "value": 2000 },
                  "maxSize": { "value": 5000 },
                  "bedrooms": { "value": 2 },
                  "bathrooms": { "value": 2 }
                },
                "selectedBoundaries": ["boundary1", "boundary2"]
              }
            }
        """.trimIndent()

        // When
        val result = Unirest
            .put(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(givenBody)
            .asString()

        // Then
        assertThat(result.status, equalTo(NOT_FOUND))
        verify(exactly = 0) { savedSearchesRepositorySpy.upsertSaveSearch(any(), any()) }
        JsonMapper.decode(result.body, RestExceptionResponse::class.java)
            .also { response ->
                assertThat(response.message, equalTo("404 | NOT_FOUND - No saved search was found with given id:b03324a1"))
            }
    }
}
