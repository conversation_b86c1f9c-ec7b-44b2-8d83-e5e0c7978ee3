package com.keyway.application.router.me

import com.keyway.adapters.dtos.me.SavedSearchIdResponse
import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.HttpMethod
import kong.unirest.HttpStatus.OK
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class CreateSavedSearchTest : BaseHttpTest(HttpMethod.POST, "/me/saved-searches") {

    private val savedSearchesRepository: SavedSearchesRepository by inject()

    @Test
    fun `Given a user with saved recent visits it should be able to add a new one`() {
        // Given
        val givenUrl = "${localUrl()}/me/saved-searches"
        val givenBody = """
            {
              "name": "Example Saved Search",
              "description": "This is an example saved search",
              "businessType": "TRIPLE_NET",
              "msaIds": ["msa123", "msa456"],
              "zipCodeIds": ["zip123", "zip456"],
              "totalProperties": 100,
              "filters": {
                "marketFilters": { 
                    "minPrice": { "value": 100000 },
                    "maxPrice": { "value": 500000 },
                    "propertyType": { "value": "Office" }
                },
                "propertyFilters": { 
                  "minSize": { "value": 2000 },
                  "maxSize": { "value": 5000 },
                  "bedrooms": { "value": 2 },
                  "bathrooms": { "value": 2 }
                },
                "selectedBoundaries": ["boundary1", "boundary2"]
              }
            }
        """.trimIndent()

        // When
        val result = Unirest
            .post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(givenBody)
            .asString()

        // Then
        assertThat(result.status, equalTo(OK))
        JsonMapper.decode(result.body, SavedSearchIdResponse::class.java)
            .also { response ->
                assertThat(response.id, equalTo("d1b82fc0-7f69-4d84-9533-7429f744d578"))
                assertNotNull(savedSearchesRepository.getSavedSearch("123", response.id))
            }
    }
}
