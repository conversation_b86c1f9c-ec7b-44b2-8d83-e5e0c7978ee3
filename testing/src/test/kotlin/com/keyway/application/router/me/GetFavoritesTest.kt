package com.keyway.application.router.me

import com.keyway.adapters.dtos.me.FavoritesResponse
import com.keyway.adapters.dtos.me.GetFavoritesResponse
import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.entities.Favorite
import com.keyway.core.repositories.FavoritesRepository
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class GetFavoritesTest : BaseHttpTest(HttpMethod.GET, "/me/favorites") {

    private val favoritesRepository: FavoritesRepository by inject()

    @BeforeEach
    fun setup() {
        favoritesRepository.saveFavorite("123", Favorite("USTX-000091", "main"))
        favoritesRepository.saveFavorite("123", Favorite("USTX-000092", null))
    }

    @Test
    fun `Given a user with saved favorites it should be able to get the list of them`() {
        // Given
        val givenUrl = "${localUrl()}/me/favorites"

        // When
        val result = Unirest
            .get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        val expectedResponse = GetFavoritesResponse(
            propertyIds = listOf("USTX-000092", "USTX-000091"),
            favorites = listOf(
                FavoritesResponse("Default", listOf("USTX-000092")),
                FavoritesResponse("main", listOf("USTX-000091"))
            )
        )

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
        JsonMapper.decode(result.body, GetFavoritesResponse::class.java)
            .let { response ->
                assertThat(response, equalTo(expectedResponse))
            }
    }
}
