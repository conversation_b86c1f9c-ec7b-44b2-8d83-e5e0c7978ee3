package com.keyway.application.router.me

import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.repositories.RecentVisitsRepository
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class AddRecentVisitTest : BaseHttpTest(HttpMethod.POST, "/me/recent-visits") {

    private val recentVisitsRepository: RecentVisitsRepository by inject()

    @BeforeEach
    fun setup() {
        recentVisitsRepository.saveRecentVisits("123", "USTX-000091")
        recentVisitsRepository.saveRecentVisits("123", "USTX-000092")
    }

    @Test
    fun `Given a user with saved recent visits it should be able to add a new one`() {
        // Given
        val givenPropertyId = "USTX-000091"
        val givenUrl = "${localUrl()}/me/recent-visits/$givenPropertyId"

        // When
        val result = Unirest
            .post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        assertThat(result.status, equalTo(HttpStatus.ACCEPTED_202))
    }
}
