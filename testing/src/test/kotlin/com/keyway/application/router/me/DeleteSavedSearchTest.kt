package com.keyway.application.router.me

import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.utils.SavedSearchesObjectMother
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.contains
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class DeleteSavedSearchTest : BaseHttpTest(HttpMethod.DELETE, "/me/saved-searches") {

    private val savedSearchesRepository: SavedSearchesRepository by inject()

    private val givenSavedSearchId = "b03324a1-f0ab-494c-9e3e-016991523c31"

    @BeforeEach
    fun setUp() {
        savedSearchesRepository.upsertSaveSearch(
            "123",
            SavedSearchesObjectMother.oneSavedSearch(id = givenSavedSearchId)
        )
    }

    @Test
    fun `Given a user with saved searches it should be able to delete one`() {
        // Given
        val givenUrl = "${localUrl()}/me/saved-searches/$givenSavedSearchId"

        // When
        val result = Unirest
            .delete(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
        assertThat(
            savedSearchesRepository.getSavedSearches("123").map { it.id },
            not(contains(givenSavedSearchId))
        )
    }
}
