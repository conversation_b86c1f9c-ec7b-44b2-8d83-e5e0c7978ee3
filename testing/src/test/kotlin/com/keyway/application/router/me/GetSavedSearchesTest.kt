package com.keyway.application.router.me

import com.keyway.adapters.dtos.me.GetSavedSearchesResponse
import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseHttpTest
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.utils.SavedSearchesObjectMother.oneSavedSearch
import com.keyway.utils.SavedSearchesObjectMother.oneSavedSearchResponse
import kong.unirest.HttpMethod
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInAnyOrder
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject

class GetSavedSearchesTest : BaseHttpTest(HttpMethod.GET, "/me/saved-searches") {

    private val savedSearchesRepository: SavedSearchesRepository by inject()

    @BeforeEach
    fun setUp() {
        savedSearchesRepository.upsertSaveSearch("123", oneSavedSearch("1"))
        savedSearchesRepository.upsertSaveSearch("123", oneSavedSearch("2"))
    }

    @Test
    fun `Given a user with saved searches it should be able to get the list of them`() {
        // Given
        val givenUrl = "${localUrl()}/me/saved-searches"

        // When
        val result = Unirest
            .get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        val expectedResponseOne = oneSavedSearchResponse("1")
        val expectedResponseTwo = oneSavedSearchResponse("2")

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
        JsonMapper.decode(result.body, GetSavedSearchesResponse::class.java)
            .let { response ->
                assertThat(response.results, containsInAnyOrder(expectedResponseOne, expectedResponseTwo))
            }
    }
}
