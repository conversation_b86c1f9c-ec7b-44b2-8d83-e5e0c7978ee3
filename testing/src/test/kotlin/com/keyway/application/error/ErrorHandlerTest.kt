package com.keyway.application.error

import com.keyway.adapters.dtos.error.response.RestExceptionResponse
import com.keyway.application.utils.AuthMock
import com.keyway.application.utils.base.BaseApplicationTest
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test

class ErrorHandlerTest : BaseApplicationTest() {

    @Test
    fun `When fail flow with an rest exception should be manage by <PERSON><PERSON>r<PERSON>and<PERSON>`() {
        // Given
        val givenUrl = "${localUrl()}/test/exception/bad-request"

        // When
        val result = Unirest
            .get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        val response = JsonMapper.decode(result.body, RestExceptionResponse::class.java)
        assertThat(response.errorCode, equalTo("BAD_REQUEST"))
        assertThat(response.message, equalTo("400 | BAD_REQUEST - Bad Request"))
        assertThat(response.statusCode, equalTo(400))
    }

    @Test
    fun `When fail flow with an exception should be manage by ErrorHandler`() {
        // Given
        val givenUrl = "${localUrl()}/test/exception/runtime"

        // When
        val result = Unirest
            .get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        val response = JsonMapper.decode(result.body, RestExceptionResponse::class.java)
        assertThat(response.errorCode, equalTo("INTERNAL_SERVER_ERROR"))
        assertThat(response.message, equalTo("500 | INTERNAL_SERVER_ERROR - Server Error"))
        assertThat(response.statusCode, equalTo(500))
    }
}
