import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val koinVersion: String by rootProject
val junitVersion: String by rootProject
val unirestVersion: String by rootProject
val javalinVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val dynamoDbVersion: String by rootProject
val hamcrestVersion: String by rootProject
val kommonsAuthVersion: String by rootProject
val mockkVersion: String by rootProject


dependencies {
    testImplementation(project(":adapters"))
    testImplementation(project(":application"))
    testImplementation(project(":core"))

    // Javalin
    testImplementation("io.javalin:javalin:$javalinVersion")

    // Mapper
    testImplementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // Rest
    testImplementation("com.konghq:unirest-objectmapper-jackson:$unirestVersion")
    testImplementation("com.konghq:unirest-java:$unirestVersion")

    // Test
    testImplementation("io.insert-koin:koin-test:$koinVersion")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:$junitVersion")
    testImplementation("org.junit.jupiter:junit-jupiter-api:$junitVersion")
    testImplementation("org.hamcrest:hamcrest-library:$hamcrestVersion")
    testImplementation("io.mockk:mockk:$mockkVersion")

    // DynamoDB
    implementation("software.amazon.awssdk:dynamodb:$dynamoDbVersion")

    // Security
    implementation("com.keyway:kommons-auth0:$kommonsAuthVersion")
}

tasks.withType<KotlinCompile>{
    kotlinOptions.jvmTarget = JavaVersion.VERSION_17.toString()
}
