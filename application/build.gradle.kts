import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar

val koinVersion: String by rootProject
val javalinVersion: String by rootProject
val jacksonVersion: String by rootProject
val commonsLangVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val unirestVersion: String by rootProject
val javalinOpenApi: String by rootProject
val dynamoDbVersion: String by rootProject
val kommonsAuthVersion: String by rootProject
val swaggerParserVersion: String by rootProject

// Application
val mainClassFqn: String = "com.keyway.application.Application"

application {
    mainClass.set(mainClassFqn)
}

plugins {
    kotlin("jvm")
    kotlin("kapt")
    application
    id("com.gradleup.shadow") version "8.3.6"
    kotlin( "plugin.serialization")
}

dependencies {
    implementation(project(":core"))
    implementation(project(":adapters"))

    // Javalin
    implementation("io.javalin:javalin:$javalinVersion")
    implementation("io.javalin.community.openapi:javalin-openapi-plugin:$javalinOpenApi")
    implementation("io.javalin.community.openapi:javalin-swagger-plugin:$javalinOpenApi")
    kapt("io.javalin.community.openapi:openapi-annotation-processor:$javalinVersion")
    // IoC
    // Koin Core features
    implementation("io.insert-koin:koin-core:$koinVersion")

    // Mapper
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVersion")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jacksonVersion")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:$jacksonVersion")
    implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
    // Mapper
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // Utils
    implementation("org.apache.commons:commons-lang3:$commonsLangVersion")

    // Security
    implementation("com.keyway:kommons-auth0:$kommonsAuthVersion")
    implementation("com.auth0:jwks-rsa:0.21.2")

    // DynamoDB
    implementation("software.amazon.awssdk:dynamodb:$dynamoDbVersion")
}

tasks {

    withType<KotlinCompile> {
        kotlinOptions.jvmTarget = JavaVersion.VERSION_17.toString()
        kotlinOptions.freeCompilerArgs = listOf("-Xopt-in=kotlin.RequiresOptIn")
    }

    named<ShadowJar>("shadowJar") {
        archiveBaseName.set("service")
        archiveClassifier.set("")
        archiveVersion.set("")
        manifest {
            attributes(mapOf("Main-Class" to application.mainClass.get()))
        }
    }

    named<JavaExec>("run") {
        doFirst {
            args = listOf("run")
        }
    }
}
