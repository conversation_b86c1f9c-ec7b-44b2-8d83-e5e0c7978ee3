system:
  api_url: "${API_URL}"
  http_port: "8080"
  timeout: 10000

rest_client:
  default_connection_timeout: "10000"
  default_socket_timeout: "30000"

datadog_config:
  step_in_seconds: 20
  api_key: "${DATADOG_API_KEY}"

security:
  enabled: "${SECURITY_ENABLED}"
  issuer: "${AUTH0_ISSUER}"
  domain: "${AUTH0_DOMAIN}"
  audiences: ${AUTH0_AUDIENCES}

aws_config:
  region: "us-east-1"
  account_id: "************"
  access_key: "${AWS_ACCESS_KEY}"
  secret_key: "${AWS_SECRET_KEY}"

dynamo_db_config:
  tables:
    - key: user_activity
      name: "${USER_ACTIVITY_TABLE}"
      timeout: "${DYNAMODB_TIMEOUT}"