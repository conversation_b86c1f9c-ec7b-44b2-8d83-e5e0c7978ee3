system:
  api_url: "http://localhost:8080"
  http_port: "8080"
  timeout: 10000

rest_client:
  default_connection_timeout: "10000"
  default_socket_timeout: "30000"

datadog_config:
  step_in_seconds: 20
  api_key: "AIzaSyD1234567890abcdefghijklmnopqrstuvwxyz"

security:
  enabled: true
  issuer: "https://auth.dev.whykeyway.com/"
  domain: "auth.dev.whykeyway.com"
  audiences: ["https://keyway-api.dev.whykeyway.com", "https://deal-room-api.dev.whykeyway.com"]

aws_config:
  region: "us-east-1"
  account_id: "************"
  access_key: "access"
  secret_key: "secret"
  endpoint_override: "http://localstack:4566"

dynamo_db_config:
  tables:
    - key: user_activity
      name: user-activity_local
      timeout: 2000