package com.keyway.application.koin.starter

import com.keyway.application.koin.modules.loader.ModuleLoader
import org.koin.core.KoinApplication
import org.koin.core.context.startKoin

object KoinStarter {

    private lateinit var koinApp: KoinApplication

    fun start(): KoinApplication {
        if (this::koinApp.isInitialized.not()) {
            koinApp = startKoin {
                modules(ModuleLoader.modules)
            }
        }
        return koinApp
    }
}
