package com.keyway.application.koin.modules

import com.auth0.jwk.UrlJwkProvider
import com.keyway.adapters.clients.DynamoDbClientBuilder
import com.keyway.adapters.executor.AsyncUseCaseExecutor
import com.keyway.adapters.executor.BaseAsyncUseCaseExecutor
import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.health.HealthCheckHandler
import com.keyway.adapters.handlers.me.AddFavoriteHandler
import com.keyway.adapters.handlers.me.AddRecentVisitHandler
import com.keyway.adapters.handlers.me.CreateSavedSearchHandler
import com.keyway.adapters.handlers.me.DeleteFavoriteHandler
import com.keyway.adapters.handlers.me.DeleteSavedSearchHandler
import com.keyway.adapters.handlers.me.GetFavoritesHandler
import com.keyway.adapters.handlers.me.GetRecentVisitsHandler
import com.keyway.adapters.handlers.me.GetSavedSearchesHandler
import com.keyway.adapters.handlers.me.UpdateSavedSearchHandler
import com.keyway.adapters.handlers.security.AuthSecurityHandler
import com.keyway.adapters.handlers.security.WhitelistedRoutesProvider
import com.keyway.adapters.repositories.FavoritesDynamoRepository
import com.keyway.adapters.repositories.KeyUUIDGenerator
import com.keyway.adapters.repositories.RecentVisitsDynamoRepository
import com.keyway.adapters.repositories.SavedSearchesDynamoRepository
import com.keyway.application.configuration.model.AwsConfig
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.DynamoDbConfig
import com.keyway.application.configuration.model.DynamoTableConfig
import com.keyway.application.configuration.model.SecurityConfig
import com.keyway.application.configuration.parser.ConfigParser
import com.keyway.application.javalin.JavalinApp
import com.keyway.application.router.health.HealthCheckRouter
import com.keyway.application.router.me.FavoritesRouter
import com.keyway.application.router.me.RecentVisitsRouter
import com.keyway.application.router.me.SavedSearchesRouter
import com.keyway.core.repositories.FavoritesRepository
import com.keyway.core.repositories.KeyGenerator
import com.keyway.core.repositories.RecentVisitsRepository
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.core.usecases.me.AddFavoriteUseCase
import com.keyway.core.usecases.me.AddRecentVisitUseCase
import com.keyway.core.usecases.me.CreateSavedSearchUseCase
import com.keyway.core.usecases.me.DeleteFavoriteUseCase
import com.keyway.core.usecases.me.DeleteSavedSearchUseCase
import com.keyway.core.usecases.me.GetFavoritesUseCase
import com.keyway.core.usecases.me.GetRecentVisitsUseCase
import com.keyway.core.usecases.me.GetSavedSearchesUseCase
import com.keyway.core.usecases.me.UpdateSavedSearchUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.security.domain.algorithm.AlgorithmRepository
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.infra.auth0.token.Auth0TokenSdk
import com.keyway.security.repository.algorithm.AlgorithmCacheRepository
import java.time.Clock
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module

object MainModule : KoinModule {

    private const val USER_ACTIVITY_TABLE_KEY = "user_activity"

    override fun get(): Module =
        module(createdAtStart = true) {
            single {
                JavalinApp.createApp(get())
            }

            // Configuration
            single { ConfigParser.read() }
            single { get<Configuration>().system }
            single { get<Configuration>().datadogConfig }
            single { get<Configuration>().awsConfig }
            single { get<Configuration>().dynamoDbConfig }
            single { get<Configuration>().security }

            single<Clock> {
                Clock.systemUTC()
            }

            // User Activity Table Config
            single {
                get<DynamoDbConfig>().tables.first {
                    it.key == USER_ACTIVITY_TABLE_KEY
                }
            }

            // Use Cases
            single<UseCaseExecutor> { BaseUseCaseExecutor }
            single<AsyncUseCaseExecutor> { BaseAsyncUseCaseExecutor }
            single { GetFavoritesUseCase(get()) }
            single { DeleteFavoriteUseCase(get()) }
            single { AddFavoriteUseCase(get()) }
            single { GetRecentVisitsUseCase(get()) }
            single { AddRecentVisitUseCase(get()) }
            single { CreateSavedSearchUseCase(get(), get()) }
            single { UpdateSavedSearchUseCase(get()) }
            single { GetSavedSearchesUseCase(get()) }
            single { DeleteSavedSearchUseCase(get()) }

            // Clients
            single {
                DynamoDbClientBuilder(
                    region = get<AwsConfig>().region,
                    secretKey = get<AwsConfig>().secretKey,
                    accessKey = get<AwsConfig>().accessKey,
                    endpointOverride = get<AwsConfig>().endpointOverride,
                    timeoutInMillis = get<DynamoTableConfig>().timeout
                ).createClient()
            }

            // Repositories
            single<FavoritesRepository> {
                FavoritesDynamoRepository(
                    client = get(),
                    tableName = get<DynamoTableConfig>().name,
                    get()
                )
            }
            single<RecentVisitsRepository> {
                RecentVisitsDynamoRepository(
                    client = get(),
                    tableName = get<DynamoTableConfig>().name,
                    get()
                )
            }
            single<SavedSearchesRepository> {
                SavedSearchesDynamoRepository(
                    client = get(),
                    tableName = get<DynamoTableConfig>().name,
                    get(),
                    JsonMapper
                )
            }
            single<KeyGenerator> {
                KeyUUIDGenerator()
            }

            // Handlers
            single { HealthCheckHandler() }
            single { GetFavoritesHandler(get(), get()) }
            single { AddFavoriteHandler(get(), get()) }
            single { DeleteFavoriteHandler(get(), get()) }

            single { GetRecentVisitsHandler(get(), get()) }
            single { AddRecentVisitHandler(get(), get()) }

            single { GetSavedSearchesHandler(get(), get()) }
            single { CreateSavedSearchHandler(get(), get()) }
            single { UpdateSavedSearchHandler(get(), get()) }
            single { DeleteSavedSearchHandler(get(), get()) }

            single { WhitelistedRoutesProvider() }
            single {
                AuthSecurityHandler(
                    get(),
                    get(),
                    get<SecurityConfig>().enabled
                )
            }

            // Routers
            single(named("routes")) {
                setOf(
                    HealthCheckRouter(get(), get()),
                    RecentVisitsRouter(get(), get(), get()),
                    FavoritesRouter(get(), get(), get(), get()),
                    SavedSearchesRouter(get(), get(), get(), get(), get())
                )
            }

            // Security
            single<AlgorithmRepository> {
                AlgorithmCacheRepository(
                    UrlJwkProvider(get<SecurityConfig>().domain)
                )
            }

            single<TokenSdk> {
                Auth0TokenSdk(
                    issuer = get<SecurityConfig>().issuer,
                    audience = get<SecurityConfig>().audiences,
                    algorithmRepository = get()
                )
            }
        }
}
