package com.keyway.application.configuration.parser

import com.keyway.application.configuration.model.Configuration
import com.keyway.kommons.mapper.YamlMapper
import org.apache.logging.log4j.util.Strings
import org.slf4j.LoggerFactory

object ConfigParser {

    private val logger = LoggerFactory.getLogger(this.javaClass)

    private lateinit var configuration: Configuration

    private const val PREFIX_ENV_KEY = "\${"
    private const val SUFFIX_ENV_KEY = "}"
    private const val LOCAL_SCOPE = "local"
    private const val SERVER_SCOPE = "server"
    private const val TEST_SCOPE = "test"

    fun read(basePath: String = "configuration", isTest: Boolean = false): Configuration {
        if (!this::configuration.isInitialized) {
            configuration = resolveEnvironment().let { environment ->
                resolveScope(isTest).let { scope ->
                    if (environment.isEmpty()) {
                        scope
                    } else {
                        "$scope-$environment"
                    }
                }
            }
                .let { configFileName ->
                    "$basePath/$configFileName.yml".let { configFileLocation ->
                        logger.info("Reading configurations from: $configFileLocation")
                        javaClass.classLoader
                            .getResourceAsStream(configFileLocation)
                            ?.reader()
                            ?.readText()
                            ?: throw IllegalStateException("Unable to find $configFileLocation")
                    }
                }
                .let(this::replaceEnvVars)
                .let { configString ->
                    YamlMapper.decode(configString, Configuration::class.java)
                }
        }

        return configuration
    }

    private fun resolveScope(isTest: Boolean): String = when {
        isTest -> TEST_SCOPE
        System.getenv("SCOPE") == null -> LOCAL_SCOPE
        else -> SERVER_SCOPE
    }

    private fun resolveEnvironment(): String = System.getenv("ENV") ?: Strings.EMPTY

    private fun replaceEnvVars(config: String): String =
        config.indexOf(PREFIX_ENV_KEY)
            .let { prefixIdx ->
                if (prefixIdx >= 0) {
                    config.indexOf(SUFFIX_ENV_KEY, prefixIdx).let { suffixIdx ->
                        config.substring(prefixIdx.plus(PREFIX_ENV_KEY.length), suffixIdx)
                    }.let { envKey ->
                        (
                            System.getenv(envKey)
                                ?: throw IllegalStateException("Environment variable $envKey was not defined.")
                            )
                            .let { envValue ->
                                replaceEnvVars(
                                    config.replace(
                                        oldValue = "$PREFIX_ENV_KEY$envKey$SUFFIX_ENV_KEY",
                                        newValue = envValue
                                    )
                                )
                            }
                    }
                } else {
                    config
                }
            }
}
