package com.keyway.application.configuration.model

import io.micrometer.datadog.DatadogConfig
import java.time.Duration

data class CustomDatadogConfig(
    val stepInSeconds: Long,
    val apiKey: String
) : DatadogConfig {

    override fun step(): Duration {
        return Duration.ofSeconds(stepInSeconds)
    }

    override fun apiKey(): String {
        return apiKey
    }

    override fun get(key: String): String? {
        return null
    }
}
