package com.keyway.application.router.me

import com.keyway.adapters.handlers.me.CreateSavedSearchHandler
import com.keyway.adapters.handlers.me.DeleteSavedSearchHandler
import com.keyway.adapters.handlers.me.GetSavedSearchesHandler
import com.keyway.adapters.handlers.me.UpdateSavedSearchHandler
import com.keyway.application.javalin.Router
import io.javalin.Javalin

class SavedSettingsRouter(
    private val app: <PERSON>lin,
    private val getSavedSettingsHandler: GetSavedSettingsHandler,
    private val deleteSavedSettingsHandler: DeleteSavedSettingsHandler,
    private val createSavedSettingsHandler: CreateSavedSettingsHandler,
) : Router {

    override fun setUpRoutes() {
        app.get("/me/settings", getSavedSettingsHandler)
        app.get("/me/settings/{id}", getSavedSettingsHandler)
        app.delete("/me/settings/{id}", deleteSavedSettingsHandler)
        app.post("/me/settings", createSavedSettingsHandler)
    }
}
