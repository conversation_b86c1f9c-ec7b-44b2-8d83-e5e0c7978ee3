package com.keyway.application.router.me

import com.keyway.adapters.handlers.me.CreateSettingsHandlers
import com.keyway.adapters.handlers.me.DeleteSettingsHandler
import com.keyway.adapters.handlers.me.GetSettingsByIdHandler
import com.keyway.adapters.handlers.me.GetSettingsHandler
import com.keyway.application.javalin.Router
import io.javalin.Javalin

class SettingsRouter(
    private val app: Javalin,
    private val getSettingsHandler: GetSettingsHandler,
    private val getSettingsByIdHandler: GetSettingsByIdHandler,
    private val deleteSettingsHandler: DeleteSettingsHandler,
    private val createSettingsHandler: CreateSettingsHandlers,
) : Router {

    override fun setUpRoutes() {
        app.get("/me/settings", getSettingsHandler)
        app.get("/me/settings/{id}", getSettingsByIdHandler)
        app.delete("/me/settings/{id}", deleteSettingsHandler)
        app.post("/me/settings", createSettingsHandler)
    }
}
