package com.keyway.application.router.me

import com.keyway.adapters.handlers.me.CreateSavedSearchHandler
import com.keyway.adapters.handlers.me.DeleteSavedSearchHandler
import com.keyway.adapters.handlers.me.GetSavedSearchesHandler
import com.keyway.adapters.handlers.me.UpdateSavedSearchHandler
import com.keyway.application.javalin.Router
import io.javalin.Javalin

class SavedSearchesRouter(
    private val app: Javalin,
    private val getSavedSearchesHandler: GetSavedSearchesHandler,
    private val deleteSavedSearchHandler: DeleteSavedSearchHandler,
    private val createSavedSearchHandler: CreateSavedSearchHandler,
    private val updateSavedSearchHandler: UpdateSavedSearchHandler
) : Router {

    override fun setUpRoutes() {
        app.get("/me/saved-searches", getSavedSearchesHandler)
        app.delete("/me/saved-searches/{id}", deleteSavedSearchHandler)
        app.post("/me/saved-searches", createSavedSearchHandler)
        app.put("/me/saved-searches/{id}", updateSavedSearchHandler)
    }
}
