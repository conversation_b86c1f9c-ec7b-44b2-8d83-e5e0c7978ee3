package com.keyway.application.router.me

import com.keyway.adapters.handlers.me.AddRecentVisitHandler
import com.keyway.adapters.handlers.me.GetRecentVisitsHandler
import com.keyway.application.javalin.Router
import io.javalin.Javalin

class RecentVisitsRouter(
    private val app: <PERSON><PERSON>,
    private val getRecentVisitsHandler: GetRecentVisitsHandler,
    private val addRecentVisitHandler: AddRecentVisitHandler
) : Router {

    override fun setUpRoutes() {
        app.get("/me/recent-visits", getRecentVisitsHandler)
        app.post("/me/recent-visits/{id}", addRecentVisitHandler)
    }
}
