package com.keyway.application.router.me

import com.keyway.adapters.handlers.me.AddFavoriteHandler
import com.keyway.adapters.handlers.me.DeleteFavoriteHandler
import com.keyway.adapters.handlers.me.GetFavoritesHandler
import com.keyway.application.javalin.Router
import io.javalin.Javalin

class FavoritesRouter(
    private val app: <PERSON>lin,
    private val getFavoritesHandler: GetFavoritesHandler,
    private val deleteFavoriteHandler: DeleteFavoriteHandler,
    private val addFavoriteHandler: AddFavoriteHandler
) : Router {

    override fun setUpRoutes() {
        app.get("/me/favorites", getFavoritesHandler)
        app.delete("/me/favorites/{id}", deleteFavoriteHandler)
        app.post("/me/favorites/{id}", addFavoriteHandler)
    }
}
