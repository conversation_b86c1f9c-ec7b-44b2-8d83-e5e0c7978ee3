package com.keyway.application

import com.keyway.adapters.handlers.security.AuthSecurityHandler
import com.keyway.application.configuration.model.CustomDatadogConfig
import com.keyway.application.configuration.model.SystemConfig
import com.keyway.application.error.ErrorHandler
import com.keyway.application.javalin.Router
import com.keyway.application.koin.starter.KoinStarter
import io.javalin.Javalin
import io.micrometer.core.instrument.Clock
import io.micrometer.core.instrument.Metrics
import io.micrometer.datadog.DatadogMeterRegistry
import org.apache.commons.lang3.RandomStringUtils
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.qualifier.named
import org.slf4j.LoggerFactory
import org.slf4j.MDC

class Application : KoinComponent {

    private val logger = LoggerFactory.getLogger(this.javaClass)
    private val app: Javalin by inject()
    private val routes: Set<Router> by inject(named("routes"))
    private val systemConfig: SystemConfig by inject()
    private val customDatadogConfig: CustomDatadogConfig by inject()
    private val authSecurityHandler: AuthSecurityHandler by inject()

    companion object {
        const val TRACE_ID = "trace_id"
        private const val TRACE_SIZE = 16

        fun generateTraceId(): String = RandomStringUtils.randomAlphanumeric(TRACE_SIZE)

        @JvmStatic
        fun main(args: Array<String>) {
            KoinStarter.start()
            Application().init()
        }
    }

    fun init() {
        MDC.put(TRACE_ID, generateTraceId())
        runCatching {
            logger.info("APP_INIT: Waiting for initialization...")

            app.before { MDC.put(TRACE_ID, generateTraceId()) }
            app.before(authSecurityHandler)
            app.after { MDC.clear() }

            Metrics.addRegistry(
                DatadogMeterRegistry(customDatadogConfig, Clock.SYSTEM)
            )

            routes.forEach {
                it.setUpRoutes()
            }

            ErrorHandler(app)

            app.start(systemConfig.httpPort).also {
                logger.info("Application already initialized and listen at port: ${systemConfig.httpPort}")
            }
        }.onFailure { e ->
            logger.error("BOOT_ERROR: " + e.message, e)
            throw e
        }.getOrDefault(Unit)
            .run { MDC.clear() }
    }
}
