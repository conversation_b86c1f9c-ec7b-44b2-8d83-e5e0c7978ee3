package com.keyway.application.error

import com.keyway.adapters.exceptions.InternalServerException
import com.keyway.adapters.exceptions.RestException
import com.keyway.application.javalin.JavalinApp
import io.javalin.Javalin
import org.slf4j.LoggerFactory

object ErrorHandler {

    private val logger = LoggerFactory.getLogger(JavalinApp::class.java)

    operator fun invoke(app: Javalin) {
        app.exception(RestException::class.java) { e, ctx ->
            e.getResponse()
                .also {
                    logger.error("[HANDLED_REST_EXCEPTION]. $it", e)
                }.let {
                    ctx.status(it.httpStatusCode).json(it)
                }
        }

        app.exception(Exception::class.java) { e, ctx ->
            InternalServerException(cause = e)
                .getResponse()
                .also {
                    logger.error("[UNHANDLED_EXCEPTION]. $it", e)
                }.let {
                    ctx.status(it.httpStatusCode).json(it)
                }
        }
    }
}
