package com.keyway.application.javalin

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.keyway.application.configuration.model.Configuration
import io.javalin.openapi.BearerAuth
import io.javalin.openapi.plugin.OpenApiPlugin
import io.javalin.openapi.plugin.swagger.SwaggerPlugin

object OpenApiConfig {

    private const val DOCUMENTATION_PATH = "/api/docs"

    fun getOpenApiConfig(config: Configuration): OpenApiPlugin =
        OpenApiPlugin {
            it.withDocumentationPath(DOCUMENTATION_PATH)
            it.withDefinitionConfiguration { _, definition ->
                definition.withInfo { info ->
                    info.title = "User Activity Api"
                    info.version = "1.0.0"
                }
                definition.withServer { openApiServer ->
                    openApiServer.url = config.system.apiUrl
                }
                definition.withSecurity { security ->
                    security.withSecurityScheme("BearerAuth", BearerAuth())
                }
                definition.withDefinitionProcessor { def ->
                    def.get("components").get("schemas").let { node ->
                        removeNullableTrue(node)
                    }
                    def.toPrettyString()
                }
            }
        }

    fun getSwaggerConfig(): SwaggerPlugin = SwaggerPlugin {
        it.documentationPath = DOCUMENTATION_PATH
    }

    private fun removeNullableTrue(node: JsonNode) {
        when (node) {
            is ObjectNode -> {
                // Get a list of field names to avoid concurrent modification
                val fieldNames = node.fieldNames().asSequence().toList()

                // Process each field
                for (fieldName in fieldNames) {
                    val childNode = node.get(fieldName)

                    // If this field is "nullable" with value true, and it's part of a property definition
                    if (fieldName == "nullable" && childNode.isBoolean && childNode.booleanValue() &&
                        (fieldNames.contains("type") || fieldNames.contains("\$ref"))
                    ) {
                        // Remove the "nullable": true field
                        node.remove("nullable")
                    } else {
                        // Recursively process child nodes
                        removeNullableTrue(childNode)
                    }
                }
            }

            is ArrayNode -> {
                // Process each element in the array
                for (i in 0 until node.size()) {
                    removeNullableTrue(node.get(i))
                }
            }
        }
    }
}
