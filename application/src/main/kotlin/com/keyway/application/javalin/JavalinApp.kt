package com.keyway.application.javalin

import com.keyway.application.configuration.model.Configuration
import com.keyway.application.mapper.AppMapperConfigs
import io.javalin.Javalin

object JavalinApp {

    private lateinit var app: Javalin

    fun createApp(configuration: Configuration): Javalin {
        if (!this::app.isInitialized) {
            app = Javalin.create { javalinConfig ->
                javalinConfig.http.asyncTimeout = 5000
                javalinConfig.bundledPlugins.enableCors { corsConfig ->
                    corsConfig.addRule { corsRule -> corsRule.anyHost() }
                }
                javalinConfig.jsonMapper(AppMapperConfigs.javalinMapper)
                javalinConfig.registerPlugin(OpenApiConfig.getOpenApiConfig(configuration))
                javalinConfig.registerPlugin(OpenApiConfig.getSwaggerConfig())
            }
        }

        return app
    }
}
