package com.keyway.core.usecases.me

import com.keyway.core.dto.me.DeleteSavedSearchInput
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.core.usecases.UseCase

class DeleteSavedSearchUseCase(
    private val savedSearchesRepository: SavedSearchesRepository
) : UseCase<DeleteSavedSearchInput, Unit> {

    override fun execute(input: DeleteSavedSearchInput) {
        savedSearchesRepository.deleteSavedSearch(input.userId, input.id)
    }
}
