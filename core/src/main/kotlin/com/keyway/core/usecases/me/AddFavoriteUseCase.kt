package com.keyway.core.usecases.me

import com.keyway.core.dto.me.AddFavoriteInput
import com.keyway.core.entities.Favorite
import com.keyway.core.repositories.FavoritesRepository
import com.keyway.core.usecases.UseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory

class AddFavoriteUseCase(
    private val favoritesRepository: FavoritesRepository
) : UseCase<AddFavoriteInput, Unit> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(input: AddFavoriteInput) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                favoritesRepository.saveFavorite(
                    userId = input.userId,
                    favorite = Favorite(
                        propertyId = input.propertyId,
                        folderName = input.folderName
                    )
                )
            } catch (e: Throwable) {
                logger.error("[ADD_FAVORITES] Error while saving favorites for user:${input.userId}, property_id:${input.propertyId}", e)
            }
        }
    }
}
