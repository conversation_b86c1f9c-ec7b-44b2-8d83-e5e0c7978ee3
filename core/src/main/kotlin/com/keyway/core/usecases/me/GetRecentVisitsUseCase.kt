package com.keyway.core.usecases.me

import com.keyway.core.dto.me.GetRecentVisitsInput
import com.keyway.core.dto.me.GetRecentVisitsOutput
import com.keyway.core.repositories.RecentVisitsRepository
import com.keyway.core.usecases.UseCase

class GetRecentVisitsUseCase(
    private val recentVisitsRepository: RecentVisitsRepository
) : UseCase<GetRecentVisitsInput, GetRecentVisitsOutput> {

    override fun execute(input: GetRecentVisitsInput): GetRecentVisitsOutput =
        GetRecentVisitsOutput(
            propertyIds = recentVisitsRepository.getRecentVisits(
                userId = input.userId,
                limit = input.limit
            )
        )
}
