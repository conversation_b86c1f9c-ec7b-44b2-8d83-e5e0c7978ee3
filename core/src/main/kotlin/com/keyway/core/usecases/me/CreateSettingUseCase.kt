package com.keyway.core.usecases.me

import com.keyway.core.dto.me.CreateSavedSearchInput
import com.keyway.core.dto.me.CreateSettingInput
import com.keyway.core.dto.me.SavedSearchIdOutput
import com.keyway.core.dto.me.SavedSettingOutput
import com.keyway.core.entities.Setting
import com.keyway.core.repositories.KeyGenerator
import com.keyway.core.repositories.SettingsRepository
import com.keyway.core.usecases.UseCase

class CreateSettingUseCase(
    private val settingsRepository: SettingsRepository,
    private val keyGenerator: KeyGenerator
) : UseCase<CreateSettingInput, SavedSettingOutput> {

    override fun execute(input: CreateSettingInput): SavedSettingOutput =
        keyGenerator.generateKey().let { id ->
            settingsRepository.upsertSetting(
                userId = input.userId,
                Setting(
                    id= input.id,
                    value = input.value
                )
            ).let { SavedSettingOutput(id) }
        }


}
