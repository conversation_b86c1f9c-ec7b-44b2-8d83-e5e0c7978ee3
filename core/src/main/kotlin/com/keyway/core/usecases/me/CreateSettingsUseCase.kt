package com.keyway.core.usecases.me

import com.keyway.core.dto.me.CreateSettingsInput
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.entities.Setting
import com.keyway.core.repositories.KeyGenerator
import com.keyway.core.repositories.SettingsRepository
import com.keyway.core.usecases.UseCase

class CreateSettingsUseCase(
    private val settingsRepository: SettingsRepository,
    private val keyGenerator: KeyGenerator
) : UseCase<CreateSettingsInput, SavedSettingsOutput> {

    override fun execute(input: CreateSettingsInput): SavedSettingsOutput =
        keyGenerator.generateKey().let { id ->
            settingsRepository.upsertSetting(
                userId = input.userId,
                Setting(
                    id= input.id,
                    value = input.value
                )
            ).let { SavedSettingsOutput(id) }
        }


}
