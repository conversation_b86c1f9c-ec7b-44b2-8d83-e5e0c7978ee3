package com.keyway.core.usecases.me

import com.keyway.core.dto.me.AddRecentVisitInput
import com.keyway.core.repositories.RecentVisitsRepository
import com.keyway.core.usecases.UseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory

class AddRecentVisitUseCase(
    private val recentVisitsRepository: RecentVisitsRepository
) : UseCase<AddRecentVisitInput, Unit> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(input: AddRecentVisitInput) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                recentVisitsRepository.saveRecentVisits(input.userId, input.propertyId)
            } catch (e: Throwable) {
                logger.error("[ADD_RECENT_VISITS] Error while saving recent visits for user:${input.userId}, property_id:${input.propertyId}", e)
            }
        }
    }
}
