package com.keyway.core.usecases.me

import com.keyway.core.dto.me.CreateSavedSearchInput
import com.keyway.core.dto.me.SavedSearchIdOutput
import com.keyway.core.entities.SavedSearch
import com.keyway.core.entities.SavedSearchFilters
import com.keyway.core.repositories.KeyGenerator
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.core.usecases.UseCase

class CreateSavedSearchUseCase(
    private val savedSearchesRepository: SavedSearchesRepository,
    private val keyGenerator: KeyGenerator
) : UseCase<CreateSavedSearchInput, SavedSearchIdOutput> {

    override fun execute(input: CreateSavedSearchInput): SavedSearchIdOutput =
        keyGenerator.generateKey().let { id ->
            savedSearchesRepository.upsertSaveSearch(
                userId = input.userId,
                savedSearch = SavedSearch(
                    id = id,
                    name = input.name,
                    description = input.description,
                    businessType = input.businessType,
                    msaIds = input.msaIds,
                    zipCodeIds = input.zipCodeIds,
                    totalProperties = input.totalProperties,
                    filters = SavedSearchFilters(
                        marketFilters = input.filters.marketFilters,
                        propertyFilters = input.filters.propertyFilters,
                        selectedBoundaries = input.filters.selectedBoundaries
                    )
                )
            ).let { SavedSearchIdOutput(id) }
        }
}
