package com.keyway.core.usecases.me

import com.keyway.core.dto.me.DeleteFavoriteInput
import com.keyway.core.repositories.FavoritesRepository
import com.keyway.core.usecases.UseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory

class DeleteFavoriteUseCase(
    private val favoritesRepository: FavoritesRepository
) : UseCase<DeleteFavoriteInput, Unit> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(input: DeleteFavoriteInput) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                favoritesRepository.deleteFavorite(input.userId, input.propertyId)
            } catch (e: Throwable) {
                logger.error("[DELETE_FAVORITES] Error while deleting favorites for user:${input.userId}, property_id:${input.propertyId}", e)
            }
        }
    }
}
