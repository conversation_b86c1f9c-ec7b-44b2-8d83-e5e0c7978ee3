package com.keyway.core.usecases.me

import com.keyway.core.dto.me.FavoritesOutput
import com.keyway.core.dto.me.GetFavoritesOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.repositories.FavoritesRepository
import com.keyway.core.usecases.UseCase

class GetFavoritesUseCase(
    private val favoritesRepository: FavoritesRepository
) : UseCase<UserIdInput, GetFavoritesOutput> {

    companion object {
        private const val DEFAULT_FOLDER_NAME = "Default"
    }

    override fun execute(input: UserIdInput): GetFavoritesOutput =
        favoritesRepository.getFavorites(input.userId).let { favorites ->
            GetFavoritesOutput(
                propertyIds = favorites.map { it.propertyId },
                favorites = favorites
                    .groupBy { it.folderName ?: DEFAULT_FOLDER_NAME }
                    .map { groupByFolder ->
                        FavoritesOutput(
                            folderName = groupByFolder.key,
                            propertyIds = groupByFolder.value.map { it.propertyId }
                        )
                    }
            )
        }
}
