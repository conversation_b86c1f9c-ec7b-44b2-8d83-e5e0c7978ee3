package com.keyway.core.usecases.me

import com.keyway.core.dto.me.FavoritesOutput
import com.keyway.core.dto.me.GetFavoritesOutput
import com.keyway.core.dto.me.SettingInput
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.repositories.SettingsRepository
import com.keyway.core.usecases.UseCase

class GetSettingsUseCase (
    private val settingsRepository: SettingsRepository
) : UseCase<SettingInput, List<SavedSettingsOutput>> {
    override fun execute(input: UserIdInput): List<SavedSettingsOutput> {
        settingsRepository.getSettings(input.userId)
            .map { t -> SavedSettingsOutput(t.id, t.value) }

    }
}

