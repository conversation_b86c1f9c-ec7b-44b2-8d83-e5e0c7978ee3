package com.keyway.core.usecases.me

import com.keyway.core.dto.me.SavedSearchIdOutput
import com.keyway.core.dto.me.UpdateSavedSearchInput
import com.keyway.core.entities.SavedSearch
import com.keyway.core.entities.SavedSearchFilters
import com.keyway.core.exceptions.SavedSearchNotFoundException
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.core.usecases.UseCase

class UpdateSavedSearchUseCase(
    private val savedSearchesRepository: SavedSearchesRepository
) : UseCase<UpdateSavedSearchInput, SavedSearchIdOutput> {

    override fun execute(input: UpdateSavedSearchInput): SavedSearchIdOutput =
        savedSearchesRepository.getSavedSearch(input.userId, input.id)
            .let { it ?: throw SavedSearchNotFoundException(input.id) }
            .let {
                savedSearchesRepository.upsertSaveSearch(
                    userId = input.userId,
                    savedSearch = SavedSearch(
                        id = input.id,
                        name = input.name,
                        description = input.description,
                        businessType = input.businessType,
                        msaIds = input.msaIds,
                        zipCodeIds = input.zipCodeIds,
                        totalProperties = input.totalProperties,
                        filters = SavedSearchFilters(
                            marketFilters = input.filters.marketFilters,
                            propertyFilters = input.filters.propertyFilters,
                            selectedBoundaries = input.filters.selectedBoundaries
                        )
                    )
                ).let { SavedSearchIdOutput(input.id) }
            }
}
