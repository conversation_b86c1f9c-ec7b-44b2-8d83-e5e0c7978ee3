package com.keyway.core.usecases.me

import com.keyway.core.dto.me.SettingInput
import com.keyway.core.repositories.SettingsRepository
import com.keyway.core.usecases.UseCase
import org.slf4j.LoggerFactory

class DeleteSettingUserCase  (
    private val settingsRepository: SettingsRepository,
) : UseCase<SettingInput, Unit> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(input: SettingInput) {
        try {
            settingsRepository.deleteSetting(input.userId, input.id)
        } catch (e: Throwable) {
            logger.error("[DELETE_FAVORITES] Error while deleting favorites for user:${input.userId}, property_id:${input.id}", e)
        }
    }
}
