package com.keyway.core.usecases.me


import com.keyway.core.dto.me.SettingInput
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.repositories.SettingsRepository
import com.keyway.core.usecases.UseCase

class GetSettingsByIdUseCase(
    private val settingsRepository: SettingsRepository
) : UseCase<SettingInput, SavedSettingsOutput?> {

    override fun execute(input: SettingInput): SavedSettingsOutput? =
        settingsRepository.getSetting(input.userId, input.id)?.let { setting ->
            SavedSettingsOutput(
                id = setting.id,
                value = setting.value
            )
        }
}

