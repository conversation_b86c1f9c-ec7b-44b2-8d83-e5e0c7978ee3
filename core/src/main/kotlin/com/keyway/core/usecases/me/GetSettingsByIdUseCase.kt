package com.keyway.core.usecases.me

import com.keyway.core.dto.me.FavoritesOutput
import com.keyway.core.dto.me.GetFavoritesOutput
import com.keyway.core.dto.me.SettingInput
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.repositories.SettingsRepository
import com.keyway.core.usecases.UseCase

class GetSettingsByIdUseCase (
    private val settingsRepository: SettingsRepository
) : UseCase<SettingInput, List<SavedSettingsOutput>> {
    override fun execute(input: UserIdInput): List<SavedSettingsOutput> {
        settingsRepository.getSettings(input.userId).let { setting ->
            GetFavoritesGeOutput(
                propertyIds = favorites.map { it.propertyId },
                favorites = favorites
                    .groupBy { it.folderName ?: DEFAULT_FOLDER_NAME }
                    .map { groupByFolder ->
                        FavoritesOutput(
                            folderName = groupByFolder.key,
                            propertyIds = groupByFolder.value.map { it.propertyId }
                        )
                    }
            )
        }
    }
}

