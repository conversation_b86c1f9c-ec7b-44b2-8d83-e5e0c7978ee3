package com.keyway.core.usecases.me

import com.keyway.core.dto.me.GetSavedSearchesOutput
import com.keyway.core.dto.me.SavedSearchFiltersOutput
import com.keyway.core.dto.me.SavedSearchOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.core.usecases.UseCase

class GetSavedSearchesUseCase(
    private val savedSearchesRepository: SavedSearchesRepository
) : UseCase<UserIdInput, GetSavedSearchesOutput> {

    override fun execute(input: UserIdInput): GetSavedSearchesOutput =
        savedSearchesRepository.getSavedSearches(input.userId)
            .map { savedSearch ->
                SavedSearchOutput(
                    id = savedSearch.id,
                    name = savedSearch.name,
                    description = savedSearch.description,
                    businessType = savedSearch.businessType,
                    msaIds = savedSearch.msaIds,
                    zipCodeIds = savedSearch.zipCodeIds,
                    totalProperties = savedSearch.totalProperties,
                    filters = SavedSearchFiltersOutput(
                        marketFilters = savedSearch.filters.marketFilters,
                        propertyFilters = savedSearch.filters.propertyFilters,
                        selectedBoundaries = savedSearch.filters.selectedBoundaries
                    )
                )
            }.let { GetSavedSearchesOutput(it) }
}
