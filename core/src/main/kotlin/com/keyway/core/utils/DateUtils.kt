package com.keyway.core.utils

import java.time.Clock
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

private val DEFAULT_ZONE_ID = ZoneOffset.UTC
private val DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneOffset.UTC)
private val defaultClock = Clock.systemUTC()

fun nowWithUTCZone(): OffsetDateTime = OffsetDateTime.now(DEFAULT_ZONE_ID)
fun OffsetDateTime.toFormatString(): String = this.format(DATE_FORMATTER)
fun nowAsFormatString(clock: Clock?): String = OffsetDateTime.now(clock ?: defaultClock).toFormatString()
