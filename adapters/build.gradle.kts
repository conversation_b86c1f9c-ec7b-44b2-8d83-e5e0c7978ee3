import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val javalinVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val javalinOpenApi: String by rootProject
val kommonsAuthVersion: String by rootProject
val dynamoDbVersion: String by rootProject
val auth0Version: String by rootProject
val swaggerParserVersion: String by rootProject

plugins {
    kotlin("jvm")
    kotlin("kapt")
}

dependencies {
    implementation(project(":core"))

    // Javalin
    implementation("io.javalin:javalin:$javalinVersion")
    kapt("io.javalin.community.openapi:openapi-annotation-processor:$javalinVersion")
    implementation("io.javalin.community.openapi:javalin-openapi-plugin:$javalinVersion")

    implementation("io.swagger.parser.v3:swagger-parser:$swaggerParserVersion")

    // Mapper
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // DynamoDB
    implementation("software.amazon.awssdk:dynamodb:$dynamoDbVersion")
    implementation("software.amazon.awssdk:netty-nio-client:$dynamoDbVersion")

    // Security
    implementation("com.keyway:kommons-auth0:$kommonsAuthVersion")
    implementation("com.auth0:jwks-rsa:$auth0Version")
}

tasks.withType<KotlinCompile> {
    kotlinOptions.jvmTarget = JavaVersion.VERSION_17.toString()
}
