package com.keyway.adapters.utils

import com.keyway.adapters.exceptions.security.SessionUserNotFoundException
import io.javalin.http.Context

fun Context.getTokenId(): String =
    this.sessionAttribute<String>("tokenId")
        ?: throw SessionUserNotFoundException()

fun Context.getUserId(): String =
    this.sessionAttribute<String>("userId")
        ?: throw SessionUserNotFoundException()

fun Context.getOptionalIntParam(name: String): Int? =
    this.queryParam(name)?.toIntOrNull()
