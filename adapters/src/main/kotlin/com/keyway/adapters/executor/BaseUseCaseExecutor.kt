package com.keyway.adapters.executor

import com.keyway.core.usecases.UseCase

object BaseUseCaseExecutor : UseCaseExecutor {

    override operator fun <InputDto, OutputDto, Input, Output> invoke(
        useCase: UseCase<Input, Output>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception
    ): OutputDto =
        runCatching {
            inputConverter(inputDto)
                .let(useCase::execute)
        }
            .onFailure { throw exceptionConverter(it) }
            .getOrThrow()
            .let(outputConverter)
}
