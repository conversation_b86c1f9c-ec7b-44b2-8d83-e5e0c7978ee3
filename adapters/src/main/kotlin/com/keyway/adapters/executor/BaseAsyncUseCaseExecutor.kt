package com.keyway.adapters.executor

import com.keyway.core.usecases.AsyncUseCase

object BaseAsyncUseCaseExecutor : AsyncUseCaseExecutor {

    override suspend operator fun <InputDto, OutputDto, Input, Output> invoke(
        useCase: AsyncUseCase<Input, Output>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception
    ): OutputDto =
        runCatching {
            useCase.execute(input = inputConverter(inputDto))
        }
            .onFailure { throw exceptionConverter(it) }
            .getOrThrow()
            .let(outputConverter)
}
