package com.keyway.adapters.executor

import com.keyway.adapters.converters.exception.ExceptionsConverter
import com.keyway.core.usecases.UseCase

interface UseCaseExecutor {
    operator fun <InputDto, OutputDto, Input, Output> invoke(
        useCase: UseCase<Input, Output>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ): OutputDto

    operator fun <InputDto, Input> invoke(
        useCase: UseCase<Input, Unit>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ) =
        invoke(useCase, inputDto, inputConverter, {}, exceptionConverter)

    operator fun invoke(
        useCase: UseCase<Unit, Unit>,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ) =
        invoke(useCase, Unit, { }, exceptionConverter)

    operator fun <OutputDto, Output> invoke(
        useCase: UseCase<Unit, Output>,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ) =
        invoke(useCase, Unit, { }, outputConverter, exceptionConverter)
}
