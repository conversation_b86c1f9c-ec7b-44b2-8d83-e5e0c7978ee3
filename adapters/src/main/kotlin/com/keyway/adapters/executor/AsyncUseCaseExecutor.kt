package com.keyway.adapters.executor

import com.keyway.adapters.converters.exception.ExceptionsConverter
import com.keyway.core.usecases.AsyncUseCase

interface AsyncUseCaseExecutor {
    suspend operator fun <InputDto, OutputDto, Input, Output> invoke(
        useCase: AsyncUseCase<Input, Output>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ): OutputDto

    suspend operator fun <InputDto, Input> invoke(
        useCase: AsyncUseCase<Input, Unit>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ) =
        invoke(useCase, inputDto, inputConverter, {}, exceptionConverter)

    suspend operator fun invoke(
        useCase: AsyncUseCase<Unit, Unit>,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ) =
        invoke(useCase, Unit, { }, exceptionConverter)

    suspend operator fun <OutputDto, Output> invoke(
        useCase: AsyncUseCase<Unit, Output>,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke
    ) =
        invoke(useCase, Unit, { }, outputConverter, exceptionConverter)
}
