package com.keyway.adapters.clients

import java.net.URI
import java.time.Duration
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.core.retry.RetryMode
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

class DynamoDbClientBuilder(
    private val region: String,
    private val secretKey: String? = null,
    private val accessKey: String? = null,
    private val endpointOverride: String? = null,
    private val timeoutInMillis: Long? = null
) {

    fun createClient(): DynamoDbAsyncClient {
        val builder = DynamoDbAsyncClient.builder()
            .region(Region.of(region))
            .httpClient(
                NettyNioAsyncHttpClient.builder()
                    .tcpKeepAlive(true)
                    .connectionTimeout(Duration.ofMillis(500))
                    .maxConcurrency(1000)
                    .tlsNegotiationTimeout(Duration.ofMillis(350))
                    .build()
            )

        if (timeoutInMillis != null) {
            val clientConfig: ClientOverrideConfiguration = ClientOverrideConfiguration.builder()
                .apiCallTimeout(Duration.ofMillis(timeoutInMillis * 2)) // Set the overall API call timeout
                .apiCallAttemptTimeout(Duration.ofMillis(timeoutInMillis)) // Set the API call attempt timeout
                .retryPolicy(RetryMode.defaultRetryMode())
                .build()

            builder.overrideConfiguration(clientConfig)
        }

        if (accessKey != null && secretKey != null) {
            builder.credentialsProvider {
                AwsBasicCredentials.create(
                    accessKey,
                    secretKey
                )
            }
        }

        if (endpointOverride != null) {
            builder.endpointOverride(URI.create(endpointOverride))
        }

        return builder.build()
    }
}
