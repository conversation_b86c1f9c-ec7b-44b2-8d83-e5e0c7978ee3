package com.keyway.adapters.exceptions

import org.eclipse.jetty.http.HttpStatus

open class RestNotFoundException(
    message: String = HttpStatus.Code.NOT_FOUND.message,
    httpStatusCode: Int = 404,
    errorCode: String = "NOT_FOUND",
    cause: Throwable? = null
) : RestException(
    message = message,
    httpStatusCode = httpStatusCode,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.NOT_FOUND.code,
    cause = cause
)
