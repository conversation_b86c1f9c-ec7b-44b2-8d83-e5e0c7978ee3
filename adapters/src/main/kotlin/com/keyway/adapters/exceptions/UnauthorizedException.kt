package com.keyway.adapters.exceptions

import org.eclipse.jetty.http.HttpStatus

open class UnauthorizedException(
    message: String = HttpStatus.Code.UNAUTHORIZED.message,
    httpStatusCode: Int = HttpStatus.Code.UNAUTHORIZED.code,
    errorCode: String = "UNAUTHORIZED",
    cause: Throwable? = null
) : RestException(
    message = message,
    httpStatusCode = httpStatusCode,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.UNAUTHORIZED.code,
    cause = cause
)
