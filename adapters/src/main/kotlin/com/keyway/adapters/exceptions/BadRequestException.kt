package com.keyway.adapters.exceptions

import org.eclipse.jetty.http.HttpStatus

open class BadRequestException(
    message: String = HttpStatus.Code.BAD_REQUEST.message,
    httpStatusCode: Int = 400,
    errorCode: String = "BAD_REQUEST",
    cause: Throwable? = null
) : RestException(
    message = message,
    httpStatusCode = httpStatusCode,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.BAD_REQUEST.code,
    cause = cause
)
