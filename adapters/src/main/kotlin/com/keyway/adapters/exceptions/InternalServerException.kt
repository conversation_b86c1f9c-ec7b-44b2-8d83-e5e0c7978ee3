package com.keyway.adapters.exceptions

import org.eclipse.jetty.http.HttpStatus

open class InternalServerException(
    message: String = HttpStatus.Code.INTERNAL_SERVER_ERROR.message,
    httpStatusCode: Int = 500,
    errorCode: String = "INTERNAL_SERVER_ERROR",
    cause: Throwable? = null
) : RestException(
    message = message,
    httpStatusCode = httpStatusCode,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.INTERNAL_SERVER_ERROR.code,
    cause = cause
)
