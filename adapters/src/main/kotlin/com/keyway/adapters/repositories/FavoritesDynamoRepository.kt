package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.asNullableString
import com.keyway.adapters.repositories.utils.asString
import com.keyway.core.entities.Favorite
import com.keyway.core.repositories.FavoritesRepository
import java.time.Clock
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

class FavoritesDynamoRepository(
    client: DynamoDbAsyncClient,
    tableName: String,
    clock: Clock
) : BaseDynamoRepository(client, tableName, clock), FavoritesRepository {

    companion object {
        private const val FAVORITES_SK_VALUE = "FAVORITES"
    }

    override fun getFavorites(userId: String): List<Favorite> =
        getItems(userId, FAVORITES_SK_VALUE)
            .takeIf { it.hasItems() }
            ?.let { response ->
                response.items()
                    .map {
                        Favorite(
                            propertyId = it.asString(SK_FIELD_NAME),
                            folderName = it.asNullableString(DATA_FIELD_NAME)
                        )
                    }
            } ?: emptyList()

    override fun saveFavorite(userId: String, favorite: Favorite) {
        updateItem(userId, FAVORITES_SK_VALUE, favorite.propertyId, favorite.folderName)
    }

    override fun deleteFavorite(userId: String, propertyId: String) {
        deleteItem(userId, FAVORITES_SK_VALUE, propertyId)
    }
}
