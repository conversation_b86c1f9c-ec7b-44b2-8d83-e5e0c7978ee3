package com.keyway.adapters.repositories

import com.keyway.core.entities.Setting
import com.keyway.core.repositories.SettingsRepository
import com.keyway.kommons.mapper.JsonMapper
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import java.time.Clock
import java.util.List

class SettingsDynamoRepository (
    client: DynamoDbAsyncClient,
    tableName: String,
    clock: Clock,
    private val mapper: JsonMapper
    ) : BaseDynamoRepository(client, tableName, clock), SettingsRepository {

    companion object {
        private const val SAVED_SETTINGS_SK_VALUE = "SETTINGS"
        private const val LIMIT = 20
    }
    override fun getSettings(userId: String): List<Setting> {
        TODO("Not yet implemented")
    }

    override fun getSetting(userId: String, settingId: String): Setting? {
        TODO("Not yet implemented")
    }

    override fun deleteSetting(userId: String, settingId: String) {
        TODO("Not yet implemented")
    }

    override fun upsertSetting(userId: String, setting: Setting) {
        updateItem(userId, SAVED_SETTINGS_SK_VALUE, setting.id, mapper.encode(setting))
    }
}
