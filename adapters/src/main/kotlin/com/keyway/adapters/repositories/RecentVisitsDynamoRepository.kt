package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.asString
import com.keyway.core.repositories.RecentVisitsRepository
import java.time.Clock
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

class RecentVisitsDynamoRepository(
    client: DynamoDbAsyncClient,
    tableName: String,
    clock: Clock
) : BaseDynamoRepository(client, tableName, clock), RecentVisitsRepository {

    companion object {
        private const val RECENT_VISITS_SK_VALUE = "RECENT_VISITS"
        private const val DEFAULT_LIMIT = 5
    }

    override fun getRecentVisits(userId: String, limit: Int?): List<String> =
        getItems(userId, RECENT_VISITS_SK_VALUE, limit ?: DEFAULT_LIMIT)
            .takeIf { it.hasItems() }
            ?.let { response ->
                response.items()
                    .map { it.asString(SK_FIELD_NAME) }
            } ?: emptyList()

    override fun saveRecentVisits(userId: String, propertyId: String) {
        updateItem(userId, RECENT_VISITS_SK_VALUE, propertyId)
    }
}
