package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.buildAttributeValueUpdate
import com.keyway.core.utils.nowAsFormatString
import java.time.Clock
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.AttributeValueUpdate
import software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest
import software.amazon.awssdk.services.dynamodb.model.QueryRequest
import software.amazon.awssdk.services.dynamodb.model.QueryResponse
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest

abstract class BaseDynamoRepository(
    private val client: DynamoDbAsyncClient,
    private val tableName: String,
    private val clock: Clock
) {

    companion object {
        private const val PK_FIELD_NAME = "PK"
        const val SK_FIELD_NAME = "SK"
        const val DATA_FIELD_NAME = "DATA"
        private const val UPDATED_AT_FIELD_NAME = "LSI1-SK"
        private const val UPDATED_AT_INDEX_NAME = "LSI1"
    }

    protected fun getItem(userId: String, entityId: String, type: String): QueryResponse {
        val queryRequest = QueryRequest.builder()
            .tableName(tableName)
            .keyConditionExpression(generateKeyConditionExpression(entityId))
            .expressionAttributeValues(generateExpressionAttributeValues(userId, type, entityId))
            .scanIndexForward(false)
            .limit(1)
            .build()

        return client.query(queryRequest).get()
    }

    protected fun getItems(userId: String, type: String, limit: Int? = 50): QueryResponse {
        val queryRequest = QueryRequest.builder()
            .tableName(tableName)
            .indexName(UPDATED_AT_INDEX_NAME)
            .keyConditionExpression(generateKeyConditionExpression())
            .expressionAttributeValues(generateExpressionAttributeValues(userId, type))
            .scanIndexForward(false)
            .limit(limit)
            .build()

        return client.query(queryRequest).get()
    }

    private fun generateExpressionAttributeValues(
        userId: String,
        type: String,
        entityId: String? = null
    ) = buildMap {
        put(":pk", AttributeValue.builder().s(composePrimaryKey(userId, type)).build())
        if (entityId != null) {
            put(":sk", AttributeValue.builder().s(entityId).build())
        }
    }

    private fun generateKeyConditionExpression(entityId: String? = null) =
        buildString {
            append("$PK_FIELD_NAME = :pk")
            if (entityId != null) {
                append(" AND $SK_FIELD_NAME = :sk")
            }
        }

    protected fun updateItem(userId: String, type: String, sk: String, data: String? = null) {
        val request = UpdateItemRequest.builder()
            .tableName(tableName)
            .key(attributeKeyMap(composePrimaryKey(userId, type), sk))
            .attributeUpdates(
                mutableMapOf<String?, AttributeValueUpdate?>(
                    UPDATED_AT_FIELD_NAME to buildAttributeValueUpdate(nowAsFormatString(clock))
                ).also {
                    if (data != null) {
                        it[DATA_FIELD_NAME] = buildAttributeValueUpdate(data)
                    }
                }
            ).build()

        client.updateItem(request).get()
    }

    protected fun deleteItem(userId: String, type: String, sk: String) {
        val request = DeleteItemRequest.builder()
            .tableName(tableName)
            .key(attributeKeyMap(composePrimaryKey(userId, type), sk))
            .build()

        client.deleteItem(request).get()
    }

    private fun composePrimaryKey(userId: String, type: String) =
        "$userId#$type"

    private fun attributeKeyMap(
        pk: String,
        sk: String
    ) = mapOf(
        PK_FIELD_NAME to AttributeValue.builder().s(pk).build(),
        SK_FIELD_NAME to AttributeValue.builder().s(sk).build()
    )
}
