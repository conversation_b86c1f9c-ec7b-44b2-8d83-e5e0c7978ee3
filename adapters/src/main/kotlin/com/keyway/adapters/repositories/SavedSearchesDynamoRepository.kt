package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.asString
import com.keyway.core.entities.SavedSearch
import com.keyway.core.repositories.SavedSearchesRepository
import com.keyway.kommons.mapper.JsonMapper
import java.time.Clock
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

class SavedSearchesDynamoRepository(
    client: DynamoDbAsyncClient,
    tableName: String,
    clock: Clock,
    private val mapper: <PERSON>sonMapper
) : BaseDynamoRepository(client, tableName, clock), SavedSearchesRepository {

    companion object {
        private const val SAVED_SEARCHES_SK_VALUE = "SAVED_SEARCHES"
        private const val LIMIT = 20
    }

    override fun getSavedSearches(userId: String): List<SavedSearch> =
        getItems(userId, SAVED_SEARCHES_SK_VALUE, LIMIT)
            .takeIf { it.hasItems() }
            ?.let { response ->
                response.items()
                    .map { it.asString(DATA_FIELD_NAME) }
                    .map { mapper.decode(it, SavedSearch::class.java) }
            } ?: emptyList()

    override fun getSavedSearch(userId: String, savedSearchId: String): SavedSearch? =
        getItem(userId, savedSearchId, SAVED_SEARCHES_SK_VALUE)
            .takeIf { it.hasItems() }
            ?.let { response ->
                response.items()
                    .map { it.asString(DATA_FIELD_NAME) }
                    .map { mapper.decode(it, SavedSearch::class.java) }
                    .firstOrNull()
            }

    override fun deleteSavedSearch(userId: String, savedSearchId: String) {
        deleteItem(userId, SAVED_SEARCHES_SK_VALUE, savedSearchId)
    }

    override fun upsertSaveSearch(userId: String, savedSearch: SavedSearch) {
        updateItem(userId, SAVED_SEARCHES_SK_VALUE, savedSearch.id, mapper.encode(savedSearch))
    }
}
