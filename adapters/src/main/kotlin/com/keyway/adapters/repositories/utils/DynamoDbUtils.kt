package com.keyway.adapters.repositories.utils

import com.keyway.adapters.exceptions.DynamoDBFieldNotFoundException
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.AttributeValueUpdate

fun Map<String, AttributeValue>.asString(fieldName: String): String =
    this.asNullableString(fieldName) ?: throw DynamoDBFieldNotFoundException(fieldName)

fun Map<String, AttributeValue>.asNullableString(fieldName: String): String? =
    this[fieldName]?.s()
fun Map<String, AttributeValue>.asListOfString(fieldName: String): List<String> =
    this.asNullableListOfString(fieldName) ?: throw DynamoDBFieldNotFoundException(fieldName)

fun Map<String, AttributeValue>.asNullableListOfString(fieldName: String): List<String>? =
    this[fieldName]?.l()?.let { set -> set.map { it.s() } }

fun buildAttributeValuePair(fieldName: String, fieldValue: String?): Pair<String, AttributeValue> =
    Pair(fieldName, buildAttributeValue(fieldValue))

fun buildAttributeValueUpdatePair(fieldName: String, fieldValue: String?): Pair<String, AttributeValueUpdate> =
    Pair(fieldName, buildAttributeValueUpdate(fieldValue))

fun buildAttributeValue(fieldValue: String?): AttributeValue =
    AttributeValue.builder().s(fieldValue).build()
fun buildAttributeValueUpdate(fieldValue: String?): AttributeValueUpdate =
    AttributeValueUpdate.builder().value(buildAttributeValue(fieldValue)).build()
