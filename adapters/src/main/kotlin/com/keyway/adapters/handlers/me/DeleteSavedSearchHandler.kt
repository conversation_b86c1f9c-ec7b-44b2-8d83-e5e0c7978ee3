package com.keyway.adapters.handlers.me

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.DeleteSavedSearchInput
import com.keyway.core.usecases.me.DeleteSavedSearchUseCase
import io.javalin.http.Context
import io.javalin.http.HttpStatus
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class DeleteSavedSearchHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val deleteSavedSearchUseCase: DeleteSavedSearchUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/saved-searches/{id}",
        methods = [HttpMethod.DELETE],
        responses = [
            OpenApiResponse(
                status = "202"
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        pathParams = [
            OpenApiParam(
                name = "id",
                type = String::class,
                description = "id of the saved search to delete",
                example = "5be06f96-519a-11ee-be56-0242ac120002"
            )
        ],
        summary = """Delete saved search.""",
        tags = ["saved-searches"],
        description = "Delete saved search."
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
            useCase = deleteSavedSearchUseCase,
            inputDto = DeleteSavedSearchInput(
                userId = ctx.getTokenId(),
                id = ctx.pathParam("id")
            ),
            inputConverter = { it }
        ).let { ctx.status(HttpStatus.OK) }
    }
}
