package com.keyway.adapters.handlers.me

import com.keyway.adapters.dtos.me.GetFavoritesResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.GetFavoritesOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.usecases.me.GetFavoritesUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class GetFavoritesHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getFavoritesUseCase: GetFavoritesUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/favorites",
        methods = [HttpMethod.GET],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = GetFavoritesResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        summary = """Get favorite properties.""",
        tags = ["favorites"],
        description = "Get Favorite properties"
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
            useCase = getFavoritesUseCase,
            inputDto = UserIdInput(ctx.getTokenId()),
            inputConverter = { it },
            outputConverter = { it.mapTo<GetFavoritesOutput, GetFavoritesResponse>() }
        ).let(ctx::json)
    }
}
