package com.keyway.adapters.handlers.security

import java.util.*

class WhitelistedRoutesProvider {
    companion object {

        private val whitelistedRoutes = mapOf<String, List<String>>(
            "/health" to listOf()
        )

        private val openApiWhitelistedRoutes = mapOf<String, List<String>>(
            "/api/docs" to listOf(),
            "/swagger" to listOf(),
            "/webjars/swagger-ui/" to listOf()
        )
    }

    fun isWhitelisted(path: String, method: String): Boolean {
        return method.uppercase(Locale.getDefault()) == "OPTIONS" || allowedPath(path, method)
    }

    private fun allowedPath(path: String, method: String): Boolean {
        return fullWhiteList.keys.firstOrNull { it.toRegex().containsMatchIn(path) }?.let { key ->
            fullWhiteList[key]!!.isEmpty() || fullWhiteList[key]!!.contains(method)
        } ?: false
    }

    private val fullWhiteList: Map<String, List<String>> by lazy {
        whitelistedRoutes.plus(openApiWhitelistedRoutes)
    }
}
