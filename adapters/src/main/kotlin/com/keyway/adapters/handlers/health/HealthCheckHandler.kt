package com.keyway.adapters.handlers.health

import com.keyway.adapters.dtos.health.HealthCheckResponse
import com.keyway.adapters.handlers.base.BaseHandler
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiResponse

class HealthCheckHandler : BaseHandler() {

    @OpenApi(
        path = "/health",
        methods = [HttpMethod.GET],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = HealthCheckResponse::class)]
            )
        ],
        summary = """Allows the infrastructure to check the health status of the service. 
        This endpoint doesn't verify database connections or another third party service status.""",
        tags = ["health"],
        description = "Health checker"
    )
    override fun handleValidated(ctx: Context) {
        ctx.json(HealthCheckResponse(status = "ok"))
    }
}
