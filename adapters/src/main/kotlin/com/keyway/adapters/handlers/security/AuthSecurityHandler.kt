package com.keyway.adapters.handlers.security

import com.keyway.adapters.exceptions.UnauthorizedException
import com.keyway.adapters.exceptions.security.AuthorizationHeaderNotFoundException
import com.keyway.adapters.exceptions.security.InvalidTokenTypeException
import com.keyway.security.domain.error.Auth0Exception
import com.keyway.security.domain.token.TokenSdk
import io.javalin.http.Context
import io.javalin.http.Handler

class AuthSecurityHandler(
    private val validateToken: TokenSdk,
    private val whitelistedRoutesProvider: WhitelistedRoutesProvider,
    private val securityEnabled: Boolean
) : Handler {

    companion object {
        private const val TOKEN_TYPE = "Bearer "
        private const val AUTHORIZATION = "Authorization"
    }

    override fun handle(ctx: Context) {
        try {
            if (securityEnabled) {
                if (!whitelistedRoutesProvider.isWhitelisted(ctx.path(), ctx.method().name)) {
                    ctx.header(AUTHORIZATION).let { it ?: throw AuthorizationHeaderNotFoundException() }
                        .takeIf { authHeader -> authHeader.contains(TOKEN_TYPE) }.let { it ?: throw InvalidTokenTypeException() }
                        .substringAfter(TOKEN_TYPE)
                        .let(validateToken::validate)
                        .let {
                            ctx.sessionAttribute("tokenId", it.id)
                            ctx.sessionAttribute("userId", it.claims.userId)
                        }
                }
            }
        } catch (e: Auth0Exception) {
            throw UnauthorizedException(message = e.localizedMessage, cause = e)
        }
    }
}
