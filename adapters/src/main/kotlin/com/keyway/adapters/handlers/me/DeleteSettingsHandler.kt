package com.keyway.adapters.handlers.me

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getUserId
import com.keyway.core.dto.me.SettingInput
import com.keyway.core.usecases.me.DeleteSettingUserCase
import io.javalin.http.Context
import io.javalin.http.HttpStatus
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class DeleteSettingsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val deleteSettingsUseCase: DeleteSettingUserCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/settings/{id}",
        methods = [HttpMethod.DELETE],
        responses = [
            OpenApiResponse(
                status = "202"
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        pathParams = [
            OpenApiParam(
                name = "id",
                type = String::class,
                description = "setting id to delete",
            )
        ],
        summary = """Delete setting.""",
        tags = ["settings"],
        description = "Delete setting"
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
            useCase = deleteSettingsUseCase,
            inputDto = SettingInput(
                userId = ctx.getUserId(),
                id  = ctx.pathParam("id")
            ),
            inputConverter = { it }
        ).let { ctx.status(HttpStatus.ACCEPTED) }
    }
}
