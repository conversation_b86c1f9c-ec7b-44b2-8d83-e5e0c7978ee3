package com.keyway.adapters.handlers.me


import com.keyway.adapters.dtos.me.CreateSettingsRequest
import com.keyway.adapters.dtos.me.SavedSettingsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getUserId
import com.keyway.core.dto.me.SettingInput
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.usecases.me.GetSettingsUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class GetSettingsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getSettingsUserCase: GetSettingsUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/settings",
        methods = [HttpMethod.GET],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSettingsResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        summary = """Get settings""",
        tags = ["settings"],
        description = "Get settings"
    )

    @OpenApi(
        path = "/me/settings/{id}",
        methods = [HttpMethod.GET],
        pathParams = [
            OpenApiParam(
                name = "id",
                type = String::class,
                description = "settings id to get",
                example = "122"
            )
        ],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSettingsResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        summary = """Get setting by Id""",
        tags = ["settings"],
        description = "Get settings"
    )
    override fun handleValidated(ctx: Context) {
        JsonMapper.decode(
            ctx.body(),
            CreateSettingsRequest::class.java
        ).let { request ->
            useCaseExecutor(
                useCase = getSettingsUserCase,
                inputDto = SettingInput(ctx.getUserId(),
                 id = ctx.pathParam("id"),
                ),
                inputConverter = { it },
                outputConverter = { it.mapTo<SavedSettingsOutput, SavedSettingsResponse>() }
            ).let { ctx.json(it) }
        }
    }
}
