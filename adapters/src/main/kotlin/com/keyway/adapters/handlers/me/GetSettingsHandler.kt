package com.keyway.adapters.handlers.me

import com.keyway.adapters.dtos.me.GetFavoritesResponse
import com.keyway.adapters.dtos.me.SavedSettingsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.adapters.utils.getUserId
import com.keyway.core.dto.me.GetFavoritesOutput
import com.keyway.core.dto.me.SettingInput
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.usecases.me.GetSettingsUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import com.keyway.kommons.mapper.utils.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class GetSettingsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getSettingsUserCase: GetSettingsUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/settings",
        methods = [HttpMethod.GET],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSettingsResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        summary = """Get settings""",
        tags = ["settings"],
        description = "Get settings"
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
                useCase = getSettingsUserCase,
                inputDto = UserIdInput(ctx.getUserId()),
                inputConverter = { it },
                outputConverter = { it.mapTo<SavedSettingsOutput, SavedSettingsResponse>() }
            ).let (ctx::json )

    }


}
