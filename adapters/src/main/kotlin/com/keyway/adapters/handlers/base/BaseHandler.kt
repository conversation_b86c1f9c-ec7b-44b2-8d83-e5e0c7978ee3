package com.keyway.adapters.handlers.base

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.adapters.exceptions.InternalServerException
import com.keyway.adapters.exceptions.RestException
import io.javalin.http.Context
import io.javalin.http.Handler
import io.javalin.validation.ValidationException

abstract class BaseHandler : Handler {

    protected abstract fun handleValidated(ctx: Context)

    final override fun handle(ctx: Context) {
        runCatching { handleValidated(ctx) }
            .onFailure { ex ->
                when (ex) {
                    is ValidationException -> throw BadRequestException(message = ex.getErrorMessage(), cause = ex)
                    is RestException -> throw ex
                    else -> throw InternalServerException(message = "Unexpected error", cause = ex)
                }
            }
            .getOrThrow()
    }

    private fun ValidationException.getErrorMessage() =
        this.errors.entries.joinToString(separator = " - ") { validationEntries ->
            "[${validationEntries.key}:${
                validationEntries.value.joinToString { validationError ->
                    validationError.message
                }
            }]"
        }
}
