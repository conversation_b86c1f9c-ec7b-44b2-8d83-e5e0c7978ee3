package com.keyway.adapters.handlers.me

import com.keyway.adapters.converters.CreateSavedSearchConverters.toInput
import com.keyway.adapters.dtos.me.CreateSavedSearchRequest
import com.keyway.adapters.dtos.me.SavedSearchIdResponse
import com.keyway.adapters.dtos.me.SavedSearchResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.SavedSearchIdOutput
import com.keyway.core.usecases.me.CreateSavedSearchUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiRequestBody
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class CreateSavedSearchHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val createSavedSearchUseCase: CreateSavedSearchUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/saved-searches",
        methods = [HttpMethod.POST],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSearchResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        requestBody = OpenApiRequestBody([OpenApiContent(CreateSavedSearchRequest::class)]),
        summary = """Create saved search.""",
        tags = ["saved-searches"],
        description = "Create saved search"
    )
    override fun handleValidated(ctx: Context) {
        JsonMapper.decode(
            ctx.body(),
            CreateSavedSearchRequest::class.java
        ).let { request ->
            useCaseExecutor(
                useCase = createSavedSearchUseCase,
                inputDto = request,
                inputConverter = { toInput(ctx.getTokenId(), request) },
                outputConverter = { it.mapTo<SavedSearchIdOutput, SavedSearchIdResponse>() }
            ).let { ctx.json(it) }
        }
    }
}
