package com.keyway.adapters.handlers.me

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.AddRecentVisitInput
import com.keyway.core.usecases.me.AddRecentVisitUseCase
import io.javalin.http.Context
import io.javalin.http.HttpStatus
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity
import kotlinx.coroutines.runBlocking

class AddRecentVisitHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val addRecentVisitUseCase: AddRecentVisitUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/recent-visits/{id}",
        methods = [HttpMethod.POST],
        responses = [
            OpenApiResponse(
                status = "200"
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        pathParams = [
            OpenApiParam(
                name = "id",
                type = String::class,
                description = "property id to add",
                example = "USTX-00001"
            )
        ],
        summary = """Add recently visited property.""",
        tags = ["recent-visits"],
        description = "Add recently visited property"
    )
    override fun handleValidated(ctx: Context) {
        runBlocking {
            useCaseExecutor(
                useCase = addRecentVisitUseCase,
                inputDto = AddRecentVisitInput(
                    userId = ctx.getTokenId(),
                    propertyId = ctx.pathParam("id")
                ),
                inputConverter = { it }
            ).let { ctx.status(HttpStatus.ACCEPTED) }
        }
    }
}
