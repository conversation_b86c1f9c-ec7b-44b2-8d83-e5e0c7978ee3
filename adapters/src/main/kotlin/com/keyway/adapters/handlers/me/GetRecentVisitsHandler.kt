package com.keyway.adapters.handlers.me

import com.keyway.adapters.dtos.me.GetRecentVisitsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getOptionalIntParam
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.GetRecentVisitsInput
import com.keyway.core.dto.me.GetRecentVisitsOutput
import com.keyway.core.usecases.me.GetRecentVisitsUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.*
import kotlinx.coroutines.runBlocking

class GetRecentVisitsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getRecentVisitsUseCase: GetRecentVisitsUseCase
) : BaseHandler() {

    companion object {
        private const val LIMIT = "limit"
    }

    @OpenApi(
        path = "/me/recent-visits",
        methods = [HttpMethod.GET],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = GetRecentVisitsResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        summary = """Get recently visited properties.""",
        queryParams = [
            OpenApiParam(
                name = LIMIT,
                type = Int::class,
                example = "5"
            )
        ],
        tags = ["recent-visits"],
        description = "Get recently visited properties"
    )
    override fun handleValidated(ctx: Context) {
        runBlocking {
            useCaseExecutor(
                useCase = getRecentVisitsUseCase,
                inputDto = GetRecentVisitsInput(
                    userId = ctx.getTokenId(),
                    limit = ctx.getOptionalIntParam(LIMIT)
                ),
                inputConverter = { it },
                outputConverter = { it.mapTo<GetRecentVisitsOutput, GetRecentVisitsResponse>() }
            ).let(ctx::json)
        }
    }
}
