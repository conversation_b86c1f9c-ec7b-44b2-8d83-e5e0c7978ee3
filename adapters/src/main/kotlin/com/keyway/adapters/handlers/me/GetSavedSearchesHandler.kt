package com.keyway.adapters.handlers.me

import com.keyway.adapters.converters.GetSavedSearchesConverters.toResponse
import com.keyway.adapters.dtos.me.GetSavedSearchesResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.UserIdInput
import com.keyway.core.usecases.me.GetSavedSearchesUseCase
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class GetSavedSearchesHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getSavedSearchesUseCase: GetSavedSearchesUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/saved-searches",
        methods = [HttpMethod.GET],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = GetSavedSearchesResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        summary = """Get saved searches.""",
        tags = ["saved-searches"],
        description = "Get saved searches"
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
            useCase = getSavedSearchesUseCase,
            inputDto = UserIdInput(ctx.getTokenId()),
            inputConverter = { it },
            outputConverter = ::toResponse
        ).let(ctx::json)
    }
}
