package com.keyway.adapters.handlers.me

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.DeleteFavoriteInput
import com.keyway.core.usecases.me.DeleteFavoriteUseCase
import io.javalin.http.Context
import io.javalin.http.HttpStatus
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class DeleteFavoriteHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val deleteFavoriteUseCase: DeleteFavoriteUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/favorites/{id}",
        methods = [HttpMethod.DELETE],
        responses = [
            OpenApiResponse(
                status = "202"
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        pathParams = [
            OpenApiParam(
                name = "id",
                type = String::class,
                description = "property id to delete",
                example = "USTX-00001"
            )
        ],
        summary = """Delete favorite property.""",
        tags = ["favorites"],
        description = "Delete Favorite property"
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
            useCase = deleteFavoriteUseCase,
            inputDto = DeleteFavoriteInput(
                userId = ctx.getTokenId(),
                propertyId = ctx.pathParam("id")
            ),
            inputConverter = { it }
        ).let { ctx.status(HttpStatus.ACCEPTED) }
    }
}
