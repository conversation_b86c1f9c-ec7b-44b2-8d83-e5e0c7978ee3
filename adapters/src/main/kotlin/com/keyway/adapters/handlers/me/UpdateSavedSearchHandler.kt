package com.keyway.adapters.handlers.me

import com.keyway.adapters.converters.UpdateSavedSearchConverters.toInput
import com.keyway.adapters.dtos.me.CreateSavedSearchRequest
import com.keyway.adapters.dtos.me.SavedSearchIdResponse
import com.keyway.adapters.dtos.me.SavedSearchResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.SavedSearchIdOutput
import com.keyway.core.usecases.me.UpdateSavedSearchUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiRequestBody
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class UpdateSavedSearchHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val updateSavedSearchUseCase: UpdateSavedSearchUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/saved-searches/{id}",
        methods = [HttpMethod.PUT],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSearchResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        requestBody = OpenApiRequestBody([OpenApiContent(CreateSavedSearchRequest::class)]),
        pathParams = [
            OpenApiParam(
                name = "id",
                type = String::class,
                description = "id of the saved search to update",
                example = "5be06f96-519a-11ee-be56-0242ac120002"
            )
        ],
        summary = """Update existing saved search.""",
        tags = ["saved-searches"],
        description = "Update existing saved search."
    )
    override fun handleValidated(ctx: Context) {
        JsonMapper.decode(
            ctx.body(),
            CreateSavedSearchRequest::class.java
        ).let { request ->
            useCaseExecutor(
                useCase = updateSavedSearchUseCase,
                inputDto = request,
                inputConverter = {
                    toInput(
                        userId = ctx.getTokenId(),
                        savedSearchId = ctx.pathParam(key = "id"),
                        request = request
                    )
                },
                outputConverter = { it.mapTo<SavedSearchIdOutput, SavedSearchIdResponse>() }
            ).let { ctx.json(it) }
        }
    }
}
