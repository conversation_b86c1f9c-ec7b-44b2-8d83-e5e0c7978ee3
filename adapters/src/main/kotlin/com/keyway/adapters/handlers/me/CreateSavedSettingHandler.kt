package com.keyway.adapters.handlers.me

import com.keyway.adapters.converters.CreateSavedSearchConverters.toInput
import com.keyway.adapters.dtos.me.CreateSettingRequest
import com.keyway.adapters.dtos.me.SavedSettingResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getUserId
import com.keyway.core.dto.me.SavedSettingOutput
import com.keyway.core.usecases.me.CreateSettingUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiRequestBody
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class CreateSettingHandlers(
    private val useCaseExecutor: UseCaseExecutor,
    private val createSettingUseCase: CreateSettingUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/settings",
        methods = [HttpMethod.POST],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSettingResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        requestBody = OpenApiRequestBody([OpenApiContent(CreateSettingRequest::class)]),
        summary = """Create setting.""",
        tags = ["settings"],
        description = "Create setting"
    )
    override fun handleValidated(ctx: Context) {
        JsonMapper.decode(
            ctx.body(),
            CreateSettingRequest::class.java
        ).let { request ->
            useCaseExecutor(
                useCase = createSettingUseCase,
                inputDto = request,
                inputConverter = { toInput(ctx.getUserId(), request) },
                outputConverter = { it.mapTo<SavedSettingOutput, SavedSettingResponse>() }
            ).let { ctx.json(it) }
        }
    }
}
