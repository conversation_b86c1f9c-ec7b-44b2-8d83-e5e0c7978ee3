package com.keyway.adapters.handlers.me


import com.keyway.adapters.dtos.me.CreateSettingsRequest
import com.keyway.adapters.dtos.me.SavedSettingsResponse
import com.keyway.core.dto.me.CreateSettingsInput
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getUserId
import com.keyway.core.dto.me.SavedSettingsOutput
import com.keyway.core.usecases.me.CreateSettingsUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.dataclass.mapTo
import io.javalin.http.Context
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiContent
import io.javalin.openapi.OpenApiRequestBody
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class CreateSettingsHandlers(
    private val useCaseExecutor: UseCaseExecutor,
    private val createSettingUseCase: CreateSettingsUseCase
) : BaseHandler() {

    @OpenApi(
        path = "/me/settings",
        methods = [HttpMethod.POST],
        responses = [
            OpenApiResponse(
                status = "200",
                content = [OpenApiContent(from = SavedSettingsResponse::class)]
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        requestBody = OpenApiRequestBody([OpenApiContent(CreateSettingsRequest::class)]),
        summary = """Create settings""",
        tags = ["settings"],
        description = "Create settings"
    )
    override fun handleValidated(ctx: Context) {
        JsonMapper.decode(
            ctx.body(),
            CreateSettingsRequest::class.java
        ).let { request ->
            useCaseExecutor(
                useCase = createSettingUseCase,
                inputDto = request,
                inputConverter = { request.mapTo<CreateSettingsRequest, CreateSettingsInput>().copy(userId = ctx.getUserId()) },
                outputConverter = { it.mapTo<SavedSettingsOutput, SavedSettingsResponse>() }
            ).let { ctx.json(it) }
        }
    }
}
