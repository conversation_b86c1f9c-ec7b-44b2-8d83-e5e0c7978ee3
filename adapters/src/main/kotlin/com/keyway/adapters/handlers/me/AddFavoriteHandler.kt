package com.keyway.adapters.handlers.me

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.base.BaseHandler
import com.keyway.adapters.utils.getTokenId
import com.keyway.core.dto.me.AddFavoriteInput
import com.keyway.core.usecases.me.AddFavoriteUseCase
import io.javalin.http.Context
import io.javalin.http.HttpStatus
import io.javalin.openapi.HttpMethod
import io.javalin.openapi.OpenApi
import io.javalin.openapi.OpenApiParam
import io.javalin.openapi.OpenApiResponse
import io.javalin.openapi.OpenApiSecurity

class AddFavoriteHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val addFavoriteUseCase: AddFavoriteUseCase
) : BaseHandler() {

    companion object {
        private const val ID = "id"
        private const val FOLDER_NAME = "folderName"
    }

    @OpenApi(
        path = "/me/favorites/{id}",
        methods = [HttpMethod.POST],
        responses = [
            OpenApiResponse(
                status = "200"
            )
        ],
        security = [OpenApiSecurity("BearerAuth", [])],
        pathParams = [
            OpenApiParam(
                name = ID,
                type = String::class,
                description = "property id to add",
                example = "USTX-000019"
            )
        ],
        queryParams = [
            OpenApiParam(
                name = FOLDER_NAME,
                type = String::class,
                description = "folder name that groups a list of favorites",
                example = "Dallas top list"
            )
        ],
        summary = """Add favorite property.""",
        tags = ["favorites"],
        description = "Add Favorite property"
    )
    override fun handleValidated(ctx: Context) {
        useCaseExecutor(
            useCase = addFavoriteUseCase,
            inputDto = AddFavoriteInput(
                userId = ctx.getTokenId(),
                propertyId = ctx.pathParam(ID),
                folderName = ctx.queryParam(FOLDER_NAME)
            ),
            inputConverter = { it }
        ).let { ctx.status(HttpStatus.ACCEPTED) }
    }
}
