package com.keyway.adapters.converters

import com.keyway.adapters.dtos.me.CreateSavedSearchRequest
import com.keyway.core.dto.me.SavedSearchFiltersInput
import com.keyway.core.dto.me.UpdateSavedSearchInput
import com.keyway.core.entities.BusinessType

object UpdateSavedSearchConverters {

    fun toInput(userId: String, savedSearchId: String, request: CreateSavedSearchRequest) =
        UpdateSavedSearchInput(
            id = savedSearchId,
            userId = userId,
            name = request.name,
            description = request.description,
            businessType = BusinessType.findByName(request.businessType.name),
            msaIds = request.msaIds,
            zipCodeIds = request.zipCodeIds,
            totalProperties = request.totalProperties,
            filters = SavedSearchFiltersInput(
                marketFilters = request.filters.marketFilters.mapValues {
                    it.value.value
                },
                propertyFilters = request.filters.propertyFilters.mapValues {
                    it.value.value
                },
                selectedBoundaries = request.filters.selectedBoundaries
            )
        )
}
