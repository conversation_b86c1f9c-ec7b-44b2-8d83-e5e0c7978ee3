package com.keyway.adapters.converters.exception

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.adapters.exceptions.InternalServerException
import com.keyway.adapters.exceptions.RestException
import com.keyway.adapters.exceptions.RestNotFoundException
import com.keyway.core.exceptions.UseCaseException
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.exceptions.base.UnexpectedException

object ExceptionsConverter {

    operator fun invoke(error: Throwable): RestException {
        return when (error) {
            is UseCaseException -> BadRequestException(
                message = error.localizedMessage,
                cause = error.cause
            )
            is NotFoundException -> RestNotFoundException(
                message = error.localizedMessage,
                cause = error.cause
            )
            is UnexpectedException -> InternalServerException(
                message = error.localizedMessage,
                cause = error.cause
            )
            else -> InternalServerException(
                message = error.localizedMessage,
                cause = error.cause
            )
        }
    }
}
