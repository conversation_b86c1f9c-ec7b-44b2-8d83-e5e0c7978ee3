package com.keyway.adapters.converters

import com.keyway.adapters.dtos.me.BusinessTypeResponse
import com.keyway.adapters.dtos.me.FilterValue
import com.keyway.adapters.dtos.me.GetSavedSearchesResponse
import com.keyway.adapters.dtos.me.SavedSearchFiltersResponse
import com.keyway.adapters.dtos.me.SavedSearchResponse
import com.keyway.core.dto.me.GetSavedSearchesOutput

object GetSavedSearchesConverters {

    fun toResponse(output: GetSavedSearchesOutput): GetSavedSearchesResponse =
        output.results.map { result ->
            SavedSearchResponse(
                id = result.id,
                name = result.name,
                description = result.description,
                businessType = BusinessTypeResponse.findByName(result.businessType.name),
                msaIds = result.msaIds,
                zipCodeIds = result.zipCodeIds,
                totalProperties = result.totalProperties,
                filters = SavedSearchFiltersResponse(
                    marketFilters = result.filters.marketFilters.mapValues {
                        FilterValue.creator(it.value)
                    },
                    propertyFilters = result.filters.propertyFilters.mapValues {
                        FilterValue.creator(it.value)
                    },
                    selectedBoundaries = result.filters.selectedBoundaries
                )
            )
        }.let { GetSavedSearchesResponse(it) }
}
