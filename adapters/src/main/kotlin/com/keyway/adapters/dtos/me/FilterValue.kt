package com.keyway.adapters.dtos.me

import com.fasterxml.jackson.annotation.JsonCreator
import com.keyway.adapters.exceptions.FilterValueTypeNotSupportedException
import io.javalin.openapi.OneOf
import io.javalin.openapi.OpenApiPropertyType
import java.math.BigDecimal

@OneOf(StringValue::class, StringArrayValue::class, IntValue::class, BooleanValue::class, BigDecimalValue::class)
sealed class FilterValue(open val value: Any) {
    companion object {
        @JsonCreator
        @JvmStatic
        fun creator(value: Any): FilterValue {
            return when (value) {
                is String -> StringValue(value)
                is Boolean -> BooleanValue(value)
                is Int -> IntValue(value)
                is BigDecimal -> BigDecimalValue(value)
                is List<*> -> {
                    when (value.firstOrNull()) {
                        is String -> StringArrayValue(value as List<String>)
                        else -> throw FilterValueTypeNotSupportedException()
                    }
                }
                else -> throw FilterValueTypeNotSupportedException()
            }
        }
    }
}

data class StringValue(override val value: String) : FilterValue(value)

data class StringArrayValue(override val value: List<String>) : FilterValue(value)

data class IntValue(override val value: Int) : FilterValue(value)

data class BigDecimalValue(
    @get:OpenApiPropertyType(definedBy = Double::class)
    override val value: BigDecimal
) : FilterValue(value)

data class BooleanValue(override val value: Boolean) : FilterValue(value)
