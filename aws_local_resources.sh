echo 'CREATING dynamodb table user_activity'
awslocal dynamodb create-table \
    --table-name user-activity_local \
    --attribute-definitions \
        AttributeName=PK,AttributeType=S \
        AttributeName=SK,AttributeType=S \
        AttributeName=LSI1-SK,AttributeType=S \
    --key-schema \
        AttributeName=PK,KeyType=HASH \
        AttributeName=SK,KeyType=RANGE \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \
    --local-secondary-indexes \
        "[{\"IndexName\": \"LSI1\",
              \"KeySchema\":[{\"AttributeName\":\"PK\",\"KeyType\":\"HASH\"},
                            {\"AttributeName\":\"LSI1-SK\",\"KeyType\":\"RANGE\"}],
              \"Projection\":{\"ProjectionType\":\"ALL\"}}]"
